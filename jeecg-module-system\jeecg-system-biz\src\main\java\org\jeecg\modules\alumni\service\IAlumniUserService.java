package org.jeecg.modules.alumni.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.alumni.entity.AlumniUser;
import org.jeecg.modules.system.entity.CheckInUserMonthSalary;

/**
 * @Description: check_in_user_month_salary
 * @Author: jeecg-boot
 * @Date:   2023-09-20
 * @Version: V1.0
 */
public interface IAlumniUserService extends IService<AlumniUser> {
    /**
     *基于openId查询或者初始校友用户信息
     * @param openId
     * @return
     */
    AlumniUser checkOrInitUserByOpenId(String openId);

    AlumniUser queryByMobile(String mobile);

    Long memberCount();

    void conceal(AlumniUser user);

}
