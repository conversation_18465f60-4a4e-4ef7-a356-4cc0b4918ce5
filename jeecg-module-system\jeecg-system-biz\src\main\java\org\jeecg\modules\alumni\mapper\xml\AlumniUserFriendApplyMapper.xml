<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.alumni.mapper.AlumniUserFriendApplyMapper">

    <select id="myFriendApplyPage" resultType="org.jeecg.modules.alumni.vo.AlumniFriendApplyVo">
                select f.id                 as friend_id,
                u.id                 as user_id,
                u.user_name          as user_name,
                u.sex                as user_sex,
                a.status             as status,
                a.create_time        as create_time,
                a.apply_time         as apply_time,
                a.approve_time       as approve_time,
                a.id                  as    apply_id,
                f.user_name            as   friend_name,
                f.sex                  as friend_sex,
                f.company              as company_name,
                f.post                 as post,
                f.phone                as phone,
               (select count(1)
                from alumni_user_friend_ref
                where friend_id = u.id
                  and user_id = f.id) as is_friend,
               (select count(1)
                from alumni_user_friend_apply
                where friend_id = f.id
                  and user_id = u.id) as is_apply
        from alumni_user_friend_apply a
                 left join alumni_user f
                           on a.user_id = f.id
                 left join alumni_user u
                           on a.friend_id = u.id
        where a.friend_id = #{userId}
        <if test="userName!=null and userName!=''">
            and  (f.user_name like CONCAT('%',#{userName},'%') or u.phone like CONCAT('%',#{userName},'%') )
        </if>
        order by a.status, a.create_time desc
    </select>

    <select id="myFriendApplyList" resultType="org.jeecg.modules.alumni.vo.AlumniFriendApplyVo">
        select f.id                 as friend_id,
               u.id                 as user_id,
               u.user_name          as user_name,
               u.sex                as user_sex,
               a.status             as status,
               a.create_time        as create_time,
               a.apply_time         as apply_time,
               a.approve_time       as approve_time,
               a.id                  as    apply_id,
               f.user_name            as   friend_name,
               f.sex                  as friend_sex,
               f.company              as company_name,
               f.post                 as post,
               f.phone                as phone,
               (select count(1)
                from alumni_user_friend_ref
                where friend_id = u.id
                  and user_id = f.id) as is_friend,
               (select count(1)
                from alumni_user_friend_apply
                where friend_id = f.id
                  and user_id = u.id) as is_apply
        from alumni_user_friend_apply a
                 left join alumni_user f
                           on a.user_id = f.id
                 left join alumni_user u
                           on a.friend_id = u.id
        where a.friend_id = #{userId}
        <if test="userName!=null and userName!=''">
            and  (f.user_name like CONCAT('%',#{userName},'%') or f.phone like CONCAT('%',#{userName},'%') )
        </if>
        order by a.status, a.create_time desc
    </select>

</mapper>