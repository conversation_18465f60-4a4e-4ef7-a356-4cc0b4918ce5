<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.alumni.mapper.AlumniMobileEventMapper">
    <select id="activityEventList" resultType="org.jeecg.modules.alumni.entity.AlumniMobileEvent">
        select * from alumni_mobile_event
        where 1=1
        <if test="activityId!=null and activityId!=''">
           AND json_extract(extra,'$.id')=#{activityId}
        </if>
        <if test="userId!=null and userId!=''">
           AND user_id=#{userId}
        </if>
        group by user_id, json_extract(extra,'$.id'),module,action,event
    </select>
</mapper>