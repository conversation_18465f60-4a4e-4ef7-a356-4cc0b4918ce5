package org.jeecg.modules.alumni.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.alumni.entity.AlumniActivityUserApply;
import org.jeecg.modules.alumni.service.IAlumniActivityUserApplyService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: alumni_activity_user_apply
 * @Author: jeecg-boot
 * @Date:   2023-10-26
 * @Version: V1.0
 */
@Api(tags="活动报名")
@RestController
@RequestMapping("/alumni/alumniActivityUserApply")
@Slf4j
public class AlumniActivityUserApplyController extends JeecgController<AlumniActivityUserApply, IAlumniActivityUserApplyService> {
	@Autowired
	private IAlumniActivityUserApplyService alumniActivityUserApplyService;
	
	/**
	 * 分页列表查询
	 *
	 * @param alumniActivityUserApply
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "alumni_activity_user_apply-分页列表查询")
	@ApiOperation(value="活动报名-分页列表查询", notes="活动报名-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<AlumniActivityUserApply>> queryPageList(AlumniActivityUserApply alumniActivityUserApply,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AlumniActivityUserApply> queryWrapper = QueryGenerator.initQueryWrapper(alumniActivityUserApply, req.getParameterMap());
		Page<AlumniActivityUserApply> page = new Page<AlumniActivityUserApply>(pageNo, pageSize);
		IPage<AlumniActivityUserApply> pageList = alumniActivityUserApplyService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param alumniActivityUserApply
	 * @return
	 */
	@AutoLog(value = "活动报名-添加")
	@ApiOperation(value="活动报名-添加", notes="活动报名-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody AlumniActivityUserApply alumniActivityUserApply) {
		alumniActivityUserApplyService.save(alumniActivityUserApply);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param alumniActivityUserApply
	 * @return
	 */
	@AutoLog(value = "活动报名-编辑")
	@ApiOperation(value="活动报名-编辑", notes="活动报名-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody AlumniActivityUserApply alumniActivityUserApply) {
		alumniActivityUserApplyService.updateById(alumniActivityUserApply);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "活动报名-通过id删除")
	@ApiOperation(value="活动报名-通过id删除", notes="活动报名-通过id删除")
	@RequiresPermissions("alumni:alumni_activity_user_apply:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		alumniActivityUserApplyService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "活动报名-批量删除")
	@ApiOperation(value="活动报名-批量删除", notes="活动报名-批量删除")
	@RequiresPermissions("alumni:alumni_activity_user_apply:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.alumniActivityUserApplyService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "alumni_activity_user_apply-通过id查询")
	@ApiOperation(value="活动报名-通过id查询", notes="活动报名-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<AlumniActivityUserApply> queryById(@RequestParam(name="id",required=true) String id) {
		AlumniActivityUserApply alumniActivityUserApply = alumniActivityUserApplyService.getById(id);
		if(alumniActivityUserApply==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(alumniActivityUserApply);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param alumniActivityUserApply
    */
    @RequiresPermissions("alumni:alumni_activity_user_apply:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AlumniActivityUserApply alumniActivityUserApply) {
        return super.exportXls(request, alumniActivityUserApply, AlumniActivityUserApply.class, "alumni_activity_user_apply");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("alumni:alumni_activity_user_apply:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AlumniActivityUserApply.class);
    }

}
