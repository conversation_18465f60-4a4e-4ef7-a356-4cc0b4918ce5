package org.jeecg.config.tencentcloud;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * cos配置.
 *
 * <AUTHOR> goku
 * @version : 1.0 2023/09/05
 * @company 深圳市超智慧信息科技有限公司
 * @organization hypersmart
 * @team 611
 * @since : 1.0
 **/

@Data
@Configuration
@ConfigurationProperties("tencent-cloud.cos")
public class TencentCloudCosConfig {
    private String bucketName;
    private String bankCardPath;
    private String idCardPath;
    private String path;
    private String url;
}
