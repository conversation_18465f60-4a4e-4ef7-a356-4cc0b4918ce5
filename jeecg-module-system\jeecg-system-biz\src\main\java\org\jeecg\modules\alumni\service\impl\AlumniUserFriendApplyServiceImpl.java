package org.jeecg.modules.alumni.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.alumni.entity.AlumniUserFriendApply;
import org.jeecg.modules.alumni.mapper.AlumniUserFriendApplyMapper;
import org.jeecg.modules.alumni.service.IAlumniUserFriendApplyService;
import org.jeecg.modules.alumni.vo.AlumniFriendApplyVo;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: alumni_user_friend_apply
 * @Author: jeecg-boot
 * @Date:   2023-11-06
 * @Version: V1.0
 */
@Service
public class AlumniUserFriendApplyServiceImpl extends ServiceImpl<AlumniUserFriendApplyMapper, AlumniUserFriendApply> implements IAlumniUserFriendApplyService {

    @Override
    public IPage<AlumniFriendApplyVo> myFriendApplyPage(Page<AlumniFriendApplyVo> page, String userId,String userName) {
        return this.getBaseMapper().myFriendApplyPage(page,userId,userName);
    }
}
