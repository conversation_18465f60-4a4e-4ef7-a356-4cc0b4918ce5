package org.jeecg.modules.alumni.controller;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollectionUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.common.util.tencent.TencentCloudCosUtils;
import org.jeecg.config.JeecgBaseConfig;
import org.jeecg.config.tencentcloud.TencentCloudConfig;
import org.jeecg.config.tencentcloud.TencentCloudCosConfig;
import org.jeecg.modules.alumni.entity.AlumniActivity;
import org.jeecg.modules.alumni.service.IAlumniActivityService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.alumni.vo.AlumniActivityApplyVo;
import org.jeecg.modules.utils.ContextUtils;
import org.jeecg.modules.utils.ImageUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

/**
 * @Description: 校友活动
 * @Author: jeecg-boot
 * @Date: 2023-10-26
 * @Version: V1.0
 */
@Api(tags = "校友活动")
@RestController
@RequestMapping("/alumni/alumniActivity")
@Slf4j
public class AlumniActivityController extends JeecgController<AlumniActivity, IAlumniActivityService> {
    @Autowired
    private IAlumniActivityService alumniActivityService;
    @Value(value = "${jeecg.path.upload}")
    private String uploadpath;
    @Resource
    private JeecgBaseConfig jeecgBaseConfig;
    @Resource
    TencentCloudConfig tencentCloudConfig;

    /**
     * 分页列表查询
     *
     * @param alumniActivity
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "校友活动-分页列表查询")
    @ApiOperation(value = "校友活动-分页列表查询", notes = "校友活动-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<AlumniActivity>> queryPageList(AlumniActivity alumniActivity,
                                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                       HttpServletRequest req) {
        QueryWrapper<AlumniActivity> queryWrapper = QueryGenerator.initQueryWrapper(alumniActivity, req.getParameterMap());
        Page<AlumniActivity> page = new Page<AlumniActivity>(pageNo, pageSize);
        IPage<AlumniActivity> pageList = alumniActivityService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 分页列表查询
     *
     * @param activityId
     * @param pageNo
     * @param pageSize
     * @return
     */
    //@AutoLog(value = "校友活动-分页列表查询")
    @ApiOperation(value = "校友活动-活动报名成员详情", notes = "校友活动-活动报名成员详情")
    @GetMapping(value = "/applyUserList")
    public Result<IPage<AlumniActivityApplyVo>> queryPageList(@RequestParam(name = "activityId",required = true) String activityId,
                                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        if(StringUtils.isEmpty(activityId)){
            throw new JeecgBootException("缺少activityId参数");
        }
        IPage<AlumniActivityApplyVo> applyVoIPage = this.service.applyUserPage(activityId, pageNo, pageSize);
        return Result.OK(applyVoIPage);
    }

    /**
     * 添加
     *
     * @param alumniActivity
     * @return
     */
    @AutoLog(value = "校友活动-添加")
    @ApiOperation(value = "校友活动-添加", notes = "校友活动-添加")
    @RequiresPermissions("alumni:alumni_activity:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody AlumniActivity alumniActivity) {
        LoginUser currentUser = ContextUtils.getCurrentUser();
        if (!Objects.isNull(currentUser)) {
            alumniActivity.setCreateBy(currentUser.getUsername());
            alumniActivity.setCreateTime(new Date());
        }
        uploadActivityCoverCosImage(alumniActivity);
        alumniActivityService.save(alumniActivity);
        return Result.OK("添加成功！");
    }

    private void uploadActivityCoverCosImage(AlumniActivity alumniActivity) {
        String coverImagePath = alumniActivity.getActivityCoverImage();
        if (StringUtils.isNotEmpty(coverImagePath)) {
            String destCosFileName = ImageUtils.getFileName(coverImagePath);
            String localSourceFilePath = uploadpath + File.separator + "temp" + File.separator + destCosFileName;
            try {
                String cosUrl = TencentCloudCosUtils.uploadLocalFile(localSourceFilePath, destCosFileName, tencentCloudConfig);
                if (StringUtils.isNotEmpty(cosUrl)) {
                    alumniActivity.setActivityCoverImageUrl(cosUrl);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 编辑
     *
     * @param alumniActivity
     * @return
     */
    @AutoLog(value = "校友活动-编辑")
    @ApiOperation(value = "校友活动-编辑", notes = "校友活动-编辑")
    @RequiresPermissions("alumni:alumni_activity:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody AlumniActivity alumniActivity) {
        LoginUser currentUser = ContextUtils.getCurrentUser();
        if (!Objects.isNull(currentUser)) {
            alumniActivity.setUpdateBy(currentUser.getUsername());
            alumniActivity.setUpdateTime(new Date());
        }
        uploadActivityCoverCosImage(alumniActivity);
        alumniActivityService.updateById(alumniActivity);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "校友活动-通过id删除")
    @ApiOperation(value = "校友活动-通过id删除", notes = "校友活动-通过id删除")
    @RequiresPermissions("alumni:alumni_activity:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        alumniActivityService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "校友活动-批量删除")
    @ApiOperation(value = "校友活动-批量删除", notes = "校友活动-批量删除")
    @RequiresPermissions("alumni:alumni_activity:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.alumniActivityService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "校友活动-通过id查询")
    @ApiOperation(value = "校友活动-通过id查询", notes = "校友活动-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<AlumniActivity> queryById(@RequestParam(name = "id", required = true) String id) {
        AlumniActivity alumniActivity = alumniActivityService.getById(id);
        if (alumniActivity == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(alumniActivity);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param alumniActivity
     */
    @RequiresPermissions("alumni:alumni_activity:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AlumniActivity alumniActivity) {
        return super.exportXls(request, alumniActivity, AlumniActivity.class, "校友活动");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("alumni:alumni_activity:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AlumniActivity.class);
    }

    /**
     * 通过excel导入数据
     *
     * @param activityId
     * @return
     */
    @RequestMapping(value = "/exportUserExcel")
    public ModelAndView importUserExcel(@RequestParam(name = "activityId",required = true) String activityId) {
        IPage<AlumniActivityApplyVo> applyVoIPage = this.service.applyUserPage(activityId, 1, 10000);
        List<AlumniActivityApplyVo> list =new ArrayList<>();
        if(Objects.nonNull(applyVoIPage)){
            list=applyVoIPage.getRecords();
        }
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        String title="活动成员列表";
        //此处设置的filename无效 ,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.FILE_NAME, title);
        mv.addObject(NormalExcelConstants.CLASS, AlumniActivityApplyVo.class);
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //update-begin--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置--------------------
        ExportParams  exportParams=new ExportParams(title + "报表", "导出人:" + sysUser.getRealname(), title);
        exportParams.setImageBasePath(jeecgBaseConfig.getPath().getUpload());
        //update-end--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置----------------------
        mv.addObject(NormalExcelConstants.PARAMS,exportParams);
        mv.addObject(NormalExcelConstants.DATA_LIST, list);
        return mv;
    }

}
