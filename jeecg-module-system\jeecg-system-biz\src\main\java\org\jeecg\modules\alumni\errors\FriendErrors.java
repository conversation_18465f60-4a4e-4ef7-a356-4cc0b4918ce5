package org.jeecg.modules.alumni.errors;

import org.jeecg.common.exception.JeecgBootError;

public class FriendErrors {
    private static final Integer ERROR_CODE=1100;

    public static final Integer ERROR_USER_NOT_EXIST=1100;
    public static final Integer ERROR_FRIEND_NOT_EXIST=1101;
    public static final Integer ERROR_FRIEND_ALREADY_EXIST=1102;
    public static final Integer ERROR_FRIEND_APPLY_EXIST=1103;
    public static final Integer ERROR_FRIEND_APPLY_NOT_EXIST=1104;

    public static final Integer ERROR_FRIEND_HAS_AGREE=1105;

    public static JeecgBootError userNotExist() {
        return new JeecgBootError(ERROR_USER_NOT_EXIST,"用户不存在");
    }

    public static JeecgBootError friendNotExist() {
        return new JeecgBootError(ERROR_FRIEND_NOT_EXIST,"好友不存在");
    }

    public static JeecgBootError friendAlreadyExist() {
        return new JeecgBootError(ERROR_FRIEND_ALREADY_EXIST,"已经是好友");
    }

    public static JeecgBootError friendApplyNotExist() {
        return new JeecgBootError(ERROR_FRIEND_APPLY_NOT_EXIST,"好友申请不存在");
    }

    public static JeecgBootError friendApplyAlreadyExist() {
        return new JeecgBootError(ERROR_FRIEND_APPLY_EXIST,"好友请求已申请");
    }

    public static JeecgBootError friendApplyAlreadyAgree() {
        return new JeecgBootError(ERROR_FRIEND_HAS_AGREE,"申请已同意");
    }

}
