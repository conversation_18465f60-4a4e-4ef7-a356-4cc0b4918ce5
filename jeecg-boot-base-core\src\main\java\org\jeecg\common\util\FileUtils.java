package org.jeecg.common.util;

import cn.hutool.core.exceptions.ExceptionUtil;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URL;
import java.nio.channels.Channels;
import java.nio.channels.FileChannel;
import java.nio.channels.ReadableByteChannel;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;

/**
 * .
 *
 * <AUTHOR> goku
 * @version : 1.0 2023/09/05
 * @company 深圳市超智慧信息科技有限公司
 * @organization hypersmart
 * @team 611
 * @since : 1.0
 **/

@Slf4j
public class FileUtils {

    public static File saveImageBase64(String savePath,String imageBase64)throws Exception{
        File imageFile=new File(savePath);
        if(!imageFile.getParentFile().exists()){
            imageFile.getParentFile().mkdirs();
        }
        if (imageBase64.contains("data:")) {
            int start = imageBase64.indexOf(",");
            imageBase64 = imageBase64.substring(start + 1);
        }
        byte[] decodedImg = Base64.getDecoder().decode(imageBase64.getBytes(StandardCharsets.UTF_8));
        Path destinationFile = Paths.get(savePath);
        Files.write(destinationFile, decodedImg);
        return imageFile;
    }

    public static void deleteFile(String path)throws Exception{
        new File(path).delete();
    }

    /**
     * 图片转Base64字符串
     * @param imageFileName
     * @return
     */
    public static String convertImageToBase64Str(String imageFileName) {
        ByteArrayOutputStream baos = null;
        try {
            //获取图片类型
            String suffix = imageFileName.substring(imageFileName.lastIndexOf(".") + 1);
            //构建文件
            File imageFile = new File(imageFileName);
            //通过ImageIO把文件读取成BufferedImage对象
            BufferedImage bufferedImage = ImageIO.read(imageFile);
            //构建字节数组输出流
            baos = new ByteArrayOutputStream();
            //写入流
            ImageIO.write(bufferedImage, suffix, baos);
            //通过字节数组流获取字节数组
            byte[] bytes = baos.toByteArray();
            //获取JDK8里的编码器Base64.Encoder转为base64字符
            return Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(ExceptionUtil.stacktraceToString(e));
        } finally {
            try {
                if (baos != null) {
                    baos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }


    public static void downloadByNIO(String url, String saveDir, String fileName) {
        ReadableByteChannel rbc = null;
        FileOutputStream fos = null;
        FileChannel foutc = null;
        try {
            rbc = Channels.newChannel(new URL(url).openStream());
            File file = new File(saveDir, fileName);
            file.getParentFile().mkdirs();
            fos = new FileOutputStream(file);
            foutc = fos.getChannel();
            foutc.transferFrom(rbc, 0, Long.MAX_VALUE);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (rbc != null) {
                try {
                    rbc.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (foutc != null) {
                try {
                    foutc.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }

    public static void main(String[] args) {
//        String imagePath="C:\\Users\\<USER>\\Pictures\\device3.png";
//        System.out.println(FileUtils.convertImageToBase64Str(imagePath));
        String url="https://611-prod-1317971397.cos.ap-guangzhou.myqcloud.com/611-app/2.0.13_35.apk";
        String saveDir="C:\\Users\\<USER>\\Downloads";
        String fileName="2.0.13_35.apk";
        FileUtils.downloadByNIO(url,saveDir,fileName);
    }

}
