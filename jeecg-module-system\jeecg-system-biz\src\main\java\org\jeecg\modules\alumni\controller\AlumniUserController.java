package org.jeecg.modules.alumni.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.servlet.ServletUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.alumni.entity.AlumniUser;
import org.jeecg.modules.alumni.service.IAlumniUserService;
import org.jeecg.modules.utils.SpellUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/oa/alumni")
@Api(tags="校友用户")
public class AlumniUserController {
    @Autowired
    private IAlumniUserService alumniUserService;
    @ApiOperation(value="校友用户-获取当前校友用户", notes="校友用户-获取当前校友用户")
    @GetMapping(value = "/queryCurrentUser")
    public Result<AlumniUser> queryCurrentUser(HttpServletRequest req) {
        Cookie tokenCookie= ServletUtil.getCookie(req,"__token__");
        String openId=tokenCookie.getValue();
        AlumniUser alumniUser=alumniUserService.checkOrInitUserByOpenId(openId);
        return Result.OK(alumniUser);
    }

    @ApiOperation(value="校友用户-保存当前校友信息", notes="校友用户-保存当前校友信息")
    @PostMapping(value = "/saveCurrentUserInfo")
    public Result<AlumniUser> saveCurrentUserInfo(@RequestBody AlumniUser userInfo,HttpServletRequest req) {
        Cookie tokenCookie= ServletUtil.getCookie(req,"__token__");
        String openId=tokenCookie.getValue();
        AlumniUser currentUser=alumniUserService.checkOrInitUserByOpenId(openId);
        BeanUtil.copyProperties(userInfo,currentUser,"id","openId");
        alumniUserService.save(currentUser);
        return Result.OK(currentUser);
    }

    @ApiOperation(value="校友用户-生成用户姓名拼音", notes="校友用户-生成用户姓名拼音")
    @PostMapping(value = "/genPinyin")
    public Result<?> genPinyin(){
        List<AlumniUser> list = alumniUserService.list();
        if(CollectionUtil.isNotEmpty(list)){
            for(AlumniUser u:list){
                if(StringUtils.isNotEmpty(u.getUserName())){
                    u.setPinyin(SpellUtils.getAllPinyin(u.getUserName()));
                }
            }
        }
        alumniUserService.saveOrUpdateBatch(list);
        return Result.ok();
    }

}
