package org.jeecg.modules.alumni.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.common.exception.JeecgBoot500Exception;
import org.jeecg.modules.alumni.entity.AlumniUser;
import org.jeecg.modules.alumni.entity.AlumniUserFriendRef;
import org.jeecg.modules.alumni.errors.FriendErrors;
import org.jeecg.modules.alumni.mapper.AlumniUserFriendRefMapper;
import org.jeecg.modules.alumni.service.IAlumniUserFriendRefService;
import org.jeecg.modules.alumni.service.IAlumniUserService;
import org.jeecg.modules.alumni.vo.AlumniUserVo;
import org.jeecg.modules.utils.SpellUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Description: alumni_user_friend_ref
 * @Author: jeecg-boot
 * @Date:   2023-11-06
 * @Version: V1.0
 */
@Service
public class AlumniUserFriendRefServiceImpl extends ServiceImpl<AlumniUserFriendRefMapper, AlumniUserFriendRef> implements IAlumniUserFriendRefService {

    @Resource
    IAlumniUserService userService;

    @Override
    public boolean isFriend(String userId, String friendId) {
        //判断是否已经是好友
        LambdaQueryWrapper<AlumniUserFriendRef> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(AlumniUserFriendRef::getUserId,userId)
                .eq(AlumniUserFriendRef::getFriendId,friendId);
        AlumniUserFriendRef friendRef=this.getOne(queryWrapper);
        return Objects.nonNull(friendRef);
    }

    @Override
    public Page<AlumniUser> queryMyFriendPage(Page<AlumniUser> page, String userId,String userName) {
        return this.getBaseMapper().queryMyFriendPage(page, userId,userName);
    }

    @Override
    public Page<AlumniUserVo> queryMemberPage(String userId,String userName, Integer pageNo, Integer pageSize) {
        Page<AlumniUserVo> alumniUserVoPage = this.getBaseMapper().queryMemberPage(new Page<>(pageNo, pageSize), userId,userName);
        if(Objects.nonNull(alumniUserVoPage)){
            if(CollectionUtil.isNotEmpty(alumniUserVoPage.getRecords())){
                List<AlumniUserVo> records
                        = alumniUserVoPage.getRecords();
                records.forEach(t->{
                    if(Objects.isNull(t.getIsFriend())||t.getIsFriend()<=0){
                        userService.conceal(t);
                    }
                });
            }
        }
        return alumniUserVoPage;
    }
}
