package org.jeecg.modules.alumni.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.alumni.entity.AlumniUserFriendApply;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.alumni.vo.AlumniFriendApplyVo;

/**
 * @Description: alumni_user_friend_apply
 * @Author: jeecg-boot
 * @Date:   2023-11-06
 * @Version: V1.0
 */
public interface IAlumniUserFriendApplyService extends IService<AlumniUserFriendApply> {
    IPage<AlumniFriendApplyVo> myFriendApplyPage(Page<AlumniFriendApplyVo> page, String userId,String userName);
}
