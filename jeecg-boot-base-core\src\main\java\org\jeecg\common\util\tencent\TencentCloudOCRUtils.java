package org.jeecg.common.util.tencent;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.BankCardOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.BankCardOCRResponse;
import com.tencentcloudapi.ocr.v20181119.models.IDCardOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.IDCardOCRResponse;
import org.jeecg.config.tencentcloud.TencentCloudConfig;

/**
 * ORC.
 *
 * <AUTHOR> goku
 * @version : 1.0 2023/09/05
 * @company 深圳市超智慧信息科技有限公司
 * @organization hypersmart
 * @team 611
 * @since : 1.0
 **/
public class TencentCloudOCRUtils {

    /**
     * 银行卡识别
     * @param req
     * @param config
     * @return
     * @throws Exception
     */
    public static BankCardOCRResponse bankCardOCRUpload(BankCardOCRRequest req, TencentCloudConfig config) throws Exception{
        Credential cred = new Credential(config.getSecretId(), config.getSecretKey());
        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("ocr.tencentcloudapi.com");
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        // 实例化要请求产品的client对象,clientProfile是可选的
        OcrClient client = new OcrClient(cred, config.getRegion(), clientProfile);
        // 返回的resp是一个BankCardOCRResponse的实例，与请求对象对应
        BankCardOCRResponse resp = client.BankCardOCR(req);
       //         输出json格式的字符串回包
        System.out.println(BankCardOCRResponse.toJsonString(resp));
        return resp;
    }


    /**
     * 身份证识别
     * @param req
     * @param config
     * @return
     * @throws Exception
     */
    public static IDCardOCRResponse idCardOCRUpload(IDCardOCRRequest req,TencentCloudConfig config)throws Exception{
        Credential cred = new Credential(config.getSecretId(), config.getSecretKey());
        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("ocr.tencentcloudapi.com");
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        // 实例化要请求产品的client对象,clientProfile是可选的
        OcrClient client = new OcrClient(cred, config.getRegion(), clientProfile);
        // 返回的resp是一个IDCardOCRResponse的实例，与请求对象对应
        IDCardOCRResponse resp = client.IDCardOCR(req);
        // 输出json格式的字符串回包
        System.out.println(IDCardOCRResponse.toJsonString(resp));
        return resp;
    }
}
