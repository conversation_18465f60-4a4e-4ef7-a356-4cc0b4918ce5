package org.jeecg.modules.alumni.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: check_in_user_month_salary
 * @Author: jeecg-boot
 * @Date:   2023-09-20
 * @Version: V1.0
 */
@Data
@TableName("alumni_user")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="alumni_user", description="毕业生用户信息")
public class AlumniUser implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
	/**微信公众号标志*/
	@Excel(name = "微信公众号标志", width = 15)
    @ApiModelProperty(value = "微信公众号标志")
    private String openId;

    @Excel(name = "用户名称", width = 15)
    @ApiModelProperty(value = "用户名称")
    private String userName;

    @Excel(name = "性别", width = 15)
    @ApiModelProperty(value = "性别：男/女")
    private String sex;

    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String phone;

    @Excel(name = "生日", width = 15)
    @ApiModelProperty(value = "生日")
    private String birth;

    @Excel(name = "毕业年份", width = 15)
    @ApiModelProperty(value = "毕业年份")
    private Integer graduateYear;

    @Excel(name = "班级", width = 15)
    @ApiModelProperty(value = "班级")
    private String classNo;

    @ApiModelProperty(value = "当前居住省编码")
    private String provinceCode;

    @Excel(name = "当前居住省", width = 15)
    @ApiModelProperty(value = "当前居住省名称")
    private String province;

    @ApiModelProperty(value = "当前居住省编码")
    private String cityCode;

    @Excel(name = "当前居住省", width = 15)
    @ApiModelProperty(value = "当前居住市名称")
    private String city;

    @ApiModelProperty(value = "当前居住区编码")
    private String regionCode;

    @Excel(name = "当前居住省", width = 15)
    @ApiModelProperty(value = "当前居住区名称")
    private String region;

    @Excel(name = "年级类型", width = 15)
    @ApiModelProperty(value = "年级类型")
    private String gradeType;

    @ApiModelProperty(value = "当前居住街道编码")
    private String streetCode;

    @Excel(name = "当前居住街道", width = 15)
    @ApiModelProperty(value = "当前居住街道名称")
    private String street;

    @Excel(name = "毕业院校", width = 15)
    @ApiModelProperty(value = "毕业院校")
    private String educationalBackground;

    @Excel(name = "大学名称", width = 15)
    @ApiModelProperty(value = "大学名称")
    private String colleague;

    @Excel(name = "最高学历", width = 15)
    @ApiModelProperty(value = "最高学历")
    private String highestEducation;

    @Excel(name = "公司", width = 15)
    @ApiModelProperty(value = "当前公司")
    private String company;

    @Excel(name = "职位", width = 15)
    @ApiModelProperty(value = "当前职位")
    private String post;

    @Excel(name = "从事的专业领域", width = 15)
    @ApiModelProperty(value = "从事的专业领域")
    private String areaExpertise;


    @ApiModelProperty(value = "公司所在省编码")
    private String companyProvinceCode;

    @Excel(name = "公司所在省", width = 15)
    @ApiModelProperty(value = "公司所在省名称")
    private String companyProvince;

    @ApiModelProperty(value = "公司所在市编码")
    private String companyCityCode;

    @Excel(name = "公司所在市", width = 15)
    @ApiModelProperty(value = "公司所在市名称")
    private String companyCity;

    @ApiModelProperty(value = "公司所在区编码")
    private String companyRegionCode;

    @Excel(name = "公司所在区", width = 15)
    @ApiModelProperty(value = "公司所在区名称")
    private String companyRegion;

    @ApiModelProperty(value = "公司所在街道编码")
    private String companyStreetCode;

    @Excel(name = "公司所在街道", width = 15)
    @ApiModelProperty(value = "公司所在街道名称")
    private String companyStreet;

    @Excel(name = "公司所在地址", width = 15)
    @ApiModelProperty(value = "公司所在地址")
    private String companyAddress;



    @ApiModelProperty(value = "家乡所在省编码")
    private String hometownProvinceCode;

    @Excel(name = "家乡所在省", width = 15)
    @ApiModelProperty(value = "家乡所在省名称")
    private String hometownProvince;

    @ApiModelProperty(value = "家乡所在市编码")
    private String hometownCityCode;

    @Excel(name = "家乡所在市", width = 15)
    @ApiModelProperty(value = "家乡所在市名称")
    private String hometownCity;

    @ApiModelProperty(value = "家乡所在区编码")
    private String hometownRegionCode;

    @Excel(name = "公司所在区", width = 15)
    @ApiModelProperty(value = "公司所在区名称")
    private String hometownRegion;

    @ApiModelProperty(value = "家乡所在街道编码")
    private String hometownStreetCode;

    @Excel(name = "公司所在街道", width = 15)
    @ApiModelProperty(value = "家乡所在街道名称")
    private String hometownStreet;

    @Excel(name = "家乡所在地址", width = 15)
    @ApiModelProperty(value = "家乡所在地址")
    private String hometownAddress;

    @Excel(name = "是否现场参加11/18校友会成立大会", width = 15)
    @ApiModelProperty(value = "是否现场参加11/18校友会成立大会 是/否")
    private String present;

    @Excel(name = "其他建议", width = 15)
    @ApiModelProperty(value = "其他建议")
    private String advice;
    @ApiModelProperty(value = "姓名拼音")
    private String pinyin;

}
