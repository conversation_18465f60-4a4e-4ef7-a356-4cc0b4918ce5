package org.jeecg.modules.alumni.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: streets
 * @Author: jeecg-boot
 * @Date:   2023-10-12
 * @Version: V1.0
 */
@Data
@TableName("streets")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="streets", description="街道")
public class Street implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "街道编码")
    private String code;

    @ApiModelProperty(value = "当前居住街道名称")
    private String name;

    @ApiModelProperty(value = "区编码")
    private String areaCode;

    @ApiModelProperty(value = "市编码")
    private String cityCode;

    @ApiModelProperty(value = "省编码")
    private String provinceCode;

}
