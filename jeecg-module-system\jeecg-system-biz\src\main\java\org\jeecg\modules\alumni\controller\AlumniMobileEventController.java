package org.jeecg.modules.alumni.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.alumni.entity.AlumniMobileEvent;
import org.jeecg.modules.alumni.service.IAlumniMobileEventService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: alumni_mobile_event
 * @Author: jeecg-boot
 * @Date:   2023-10-26
 * @Version: V1.0
 */
@Api(tags="alumni_mobile_event")
@RestController
@RequestMapping("/alumni/alumniMobileEvent")
@Slf4j
public class AlumniMobileEventController extends JeecgController<AlumniMobileEvent, IAlumniMobileEventService> {
	@Autowired
	private IAlumniMobileEventService alumniMobileEventService;
	
	/**
	 * 分页列表查询
	 *
	 * @param alumniMobileEvent
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "alumni_mobile_event-分页列表查询")
	@ApiOperation(value="alumni_mobile_event-分页列表查询", notes="alumni_mobile_event-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<AlumniMobileEvent>> queryPageList(AlumniMobileEvent alumniMobileEvent,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AlumniMobileEvent> queryWrapper = QueryGenerator.initQueryWrapper(alumniMobileEvent, req.getParameterMap());
		Page<AlumniMobileEvent> page = new Page<AlumniMobileEvent>(pageNo, pageSize);
		IPage<AlumniMobileEvent> pageList = alumniMobileEventService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param alumniMobileEvent
	 * @return
	 */
	@AutoLog(value = "alumni_mobile_event-添加")
	@ApiOperation(value="alumni_mobile_event-添加", notes="alumni_mobile_event-添加")
	@RequiresPermissions("alumni:alumni_mobile_event:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody AlumniMobileEvent alumniMobileEvent) {
		alumniMobileEventService.save(alumniMobileEvent);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param alumniMobileEvent
	 * @return
	 */
	@AutoLog(value = "alumni_mobile_event-编辑")
	@ApiOperation(value="alumni_mobile_event-编辑", notes="alumni_mobile_event-编辑")
	@RequiresPermissions("alumni:alumni_mobile_event:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody AlumniMobileEvent alumniMobileEvent) {
		alumniMobileEventService.updateById(alumniMobileEvent);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "alumni_mobile_event-通过id删除")
	@ApiOperation(value="alumni_mobile_event-通过id删除", notes="alumni_mobile_event-通过id删除")
	@RequiresPermissions("alumni:alumni_mobile_event:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		alumniMobileEventService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "alumni_mobile_event-批量删除")
	@ApiOperation(value="alumni_mobile_event-批量删除", notes="alumni_mobile_event-批量删除")
	@RequiresPermissions("alumni:alumni_mobile_event:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.alumniMobileEventService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "alumni_mobile_event-通过id查询")
	@ApiOperation(value="alumni_mobile_event-通过id查询", notes="alumni_mobile_event-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<AlumniMobileEvent> queryById(@RequestParam(name="id",required=true) String id) {
		AlumniMobileEvent alumniMobileEvent = alumniMobileEventService.getById(id);
		if(alumniMobileEvent==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(alumniMobileEvent);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param alumniMobileEvent
    */
    @RequiresPermissions("alumni:alumni_mobile_event:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AlumniMobileEvent alumniMobileEvent) {
        return super.exportXls(request, alumniMobileEvent, AlumniMobileEvent.class, "alumni_mobile_event");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("alumni:alumni_mobile_event:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AlumniMobileEvent.class);
    }

}
