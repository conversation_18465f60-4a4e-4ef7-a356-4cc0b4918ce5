package org.jeecg.modules.alumni.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.DateUtils;
import org.jeecg.modules.alumni.entity.AlumniActivity;
import org.jeecg.modules.alumni.entity.AlumniActivityUserApply;
import org.jeecg.modules.alumni.entity.AlumniMobileEvent;
import org.jeecg.modules.alumni.entity.AlumniUser;
import org.jeecg.modules.alumni.enums.ActivityConstants;
import org.jeecg.modules.alumni.enums.EventConstants;
import org.jeecg.modules.alumni.mapper.AlumniActivityMapper;
import org.jeecg.modules.alumni.mapper.AlumniActivityUserApplyMapper;
import org.jeecg.modules.alumni.mapper.AlumniMobileEventMapper;
import org.jeecg.modules.alumni.service.IAlumniActivityService;
import org.jeecg.modules.alumni.service.IAlumniActivityUserApplyService;
import org.jeecg.modules.alumni.service.IAlumniUserService;
import org.jeecg.modules.alumni.vo.AlumniActivityApplyVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * .
 *
 * <AUTHOR> goku
 * @version : 1.0 2023/10/26
 * @company 深圳市超智慧信息科技有限公司
 * @organization hypersmart
 * @team 611
 * @since : 1.0
 **/
@Service
public class AlumniActivityServiceImpl extends ServiceImpl<AlumniActivityMapper, AlumniActivity> implements IAlumniActivityService {
    @Resource
    private IAlumniUserService userService;

    @Resource
    private  IAlumniActivityUserApplyService alumniActivityUserApplyService;

    @Resource
    private AlumniMobileEventMapper mobileEventMapper;
    @Override
    public String queryMaxActivityNo() {
        return getBaseMapper().queryMaxActivityNo();
    }

    @Override
    public void applyActivity(String activityId, String userId) throws Exception{
        AlumniActivity activity = getById(activityId);
        if(Objects.isNull(activity)){
            throw new JeecgBootException("活动不存在");
        }
        AlumniUser user=userService.getById(userId);
        if(Objects.isNull(user)){
            throw new JeecgBootException("账号不存在");
        }
        Date now = new Date();
        if(activity.getSignInDeadline().getTime() <= now.getTime()){
            throw new JeecgBootException("活动报名已截止");
        }
        LambdaQueryWrapper<AlumniActivityUserApply> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(AlumniActivityUserApply::getUserId,userId)
                .eq(AlumniActivityUserApply::getActivityId,activityId);
        AlumniActivityUserApply apply=alumniActivityUserApplyService.getOne(queryWrapper);
        if(Objects.nonNull(apply)){
            throw new JeecgBootException("您已报名该活动");
        }else{
            AlumniActivityUserApply alumniActivityUserApply=new AlumniActivityUserApply();
            alumniActivityUserApply.setActivityId(activityId);
            alumniActivityUserApply.setUserId(userId);
            alumniActivityUserApply.setApplyTime(now);
            alumniActivityUserApplyService.save(alumniActivityUserApply);
        }
    }

    @Override
    public AlumniActivity userActivity(String activityId, String userId) {
        AlumniActivity activity = getById(activityId);
        if(Objects.isNull(activity)){
            throw new JeecgBootException("活动不存在");
        }
        LambdaQueryWrapper<AlumniActivityUserApply> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(AlumniActivityUserApply::getUserId,userId)
                .eq(AlumniActivityUserApply::getActivityId,activityId);
        AlumniActivityUserApply apply=alumniActivityUserApplyService.getOne(queryWrapper);
        if(Objects.nonNull(apply)){
         activity.setHasApply(true);
         activity.setApplyTime(apply.getApplyTime());
        }
        return activity;
    }

    @Override
    public List<AlumniActivity> userApplyActivityList(String userId, Integer status) {
        return getBaseMapper().userApplyActivityList(userId,status);
    }

    @Override
    public IPage<AlumniActivity> userApplyActivityPage(String userId, Integer status, Integer pageNo, Integer pageSize) {
        Page<AlumniActivity> page=new Page<>(pageNo,pageSize);
        return getBaseMapper().userApplyActivityPage(page,userId,status);
    }

    @Override
    public IPage<AlumniActivityApplyVo> applyUserPage(String activityId, Integer pageNo, Integer pageSize) {
        IPage<AlumniActivityApplyVo> applyVoIPage = getBaseMapper().applyUserList(new Page<>(pageNo, pageSize), activityId);
        if(Objects.nonNull(applyVoIPage)){
            List<AlumniActivityApplyVo> vos=applyVoIPage.getRecords();
            if(CollectionUtil.isNotEmpty(vos)){
                List<AlumniMobileEvent> events = mobileEventMapper.activityEventList(activityId, null);
                for(AlumniActivityApplyVo vo:vos){
                    if(CollectionUtil.isNotEmpty(events)){
                        Optional<AlumniMobileEvent> itemApply = events.stream()
                                .filter(e -> Objects.equals(EventConstants.ACTIVITY_ITEM_APPLY, e.getEvent()))
                                .findFirst();
                        if(itemApply.isPresent()){
                            vo.setHomeApply(ActivityConstants.APPLYED);
                        }
                        Optional<AlumniMobileEvent> detailApply = events.stream()
                                .filter(e -> Objects.equals(EventConstants.ACTIVITY_DETAIL_APPLY, e.getEvent()))
                                .findFirst();
                        if(detailApply.isPresent()){
                            vo.setDetailApply(ActivityConstants.APPLYED);
                        }
                    }
                }
            }
        }
        return applyVoIPage;
    }

    @Override
    public Long effectiveCount() {
        LambdaQueryWrapper<AlumniActivity> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.gt(AlumniActivity::getSignInDeadline,new Date())
                .eq(AlumniActivity::getEnable,ActivityConstants.ENABLE)
                .eq(AlumniActivity::getIsDel,CommonConstant.DEL_FLAG_0)
                .ne(AlumniActivity::getStatus,ActivityConstants.ACTIVITY_STATUS_ENDED);
        return getBaseMapper().selectCount(queryWrapper);
    }

    @Override
    @Transactional
    public void activityJob() {
        LambdaQueryWrapper<AlumniActivity> queryWrapper=new LambdaQueryWrapper<>();
//        queryWrapper.eq(AlumniActivity::getStatus, CommonConstant.NO);
        List<AlumniActivity> activities=this.list(queryWrapper);
        if(CollectionUtil.isNotEmpty(activities)){
            for(AlumniActivity activity:activities){
                Date current =new Date();
                Date effectTime =activity.getActivityDate();
                String today= DateUtils.formatDate(current);
                String activityDate=DateUtils.formatDate(effectTime);
                if(Objects.equals(today,activityDate)){
                    activity.setStatus(ActivityConstants.ACTIVITY_STATUS_STARTED);
                }
                else if(effectTime.getTime()>current.getTime()){
                    activity.setStatus(ActivityConstants.ACTIVITY_STATUS_NO_STARTED);
                }else{
                    activity.setStatus(ActivityConstants.ACTIVITY_STATUS_ENDED);
                }
            }
            updateBatchById(activities);
        }
    }
}
