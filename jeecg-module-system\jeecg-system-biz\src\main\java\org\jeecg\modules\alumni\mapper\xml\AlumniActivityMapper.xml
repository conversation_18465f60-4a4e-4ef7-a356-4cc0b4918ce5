<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.alumni.mapper.AlumniActivityMapper">


    <select id="queryMaxActivityNo" resultType="java.lang.String">
        select max(activity_no) from alumni_activity
    </select>


    <select id="applyUserList" resultType="org.jeecg.modules.alumni.vo.AlumniActivityApplyVo">
        select u.*, a.apply_time, v.activity_no, v.activity_name
        from alumni_user u
                 left join alumni_activity_user_apply a
                           on u.id = a.user_id
                 left join alumni_activity v
                           on a.activity_id = v.id
        where 1=1
        <if test="activityId!=null and activityId!=''">
            and   v.id = #{activityId}
        </if>
        order by a.apply_time desc
    </select>

    <select id="userApplyActivityList" resultType="org.jeecg.modules.alumni.entity.AlumniActivity">
        select a.*,u.apply_time from alumni_activity a
                              left join alumni_activity_user_apply u
                                        on a.id = u.activity_id
        where u.user_id=#{userId}
        and a.enable='Y' and a.is_del=0
        <if test="status!=null">
            and a.status=#{status}
        </if>
        order by a.create_time desc
    </select>

    <select id="userApplyActivityPage" resultType="org.jeecg.modules.alumni.entity.AlumniActivity">
        select  a.*,u.apply_time from alumni_activity a
        left join alumni_activity_user_apply u
        on a.id = u.activity_id
        where u.user_id=#{userId}
        and a.enable='Y' and a.is_del=0
        <if test="status!=null">
            and a.status=#{status}
        </if>
        order by a.create_time desc
    </select>
</mapper>