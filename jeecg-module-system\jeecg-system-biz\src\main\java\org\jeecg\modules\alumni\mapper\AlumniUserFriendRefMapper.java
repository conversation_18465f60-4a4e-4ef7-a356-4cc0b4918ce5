package org.jeecg.modules.alumni.mapper;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.alumni.entity.AlumniUser;
import org.jeecg.modules.alumni.entity.AlumniUserFriendRef;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.alumni.vo.AlumniUserVo;

/**
 * @Description: alumni_user_friend_ref
 * @Author: jeecg-boot
 * @Date:   2023-11-06
 * @Version: V1.0
 */
public interface AlumniUserFriendRefMapper extends BaseMapper<AlumniUserFriendRef> {
    List<AlumniUser> queryMyFriendList(@Param("userId") String userId);

    Page<AlumniUser> queryMyFriendPage(Page<AlumniUser> page, @Param("userId") String userId,@Param("userName") String userName);

    Page<AlumniUserVo> queryMemberPage(Page<AlumniUserVo> page, @Param("userId") String userId,@Param("userName") String userName);
}
