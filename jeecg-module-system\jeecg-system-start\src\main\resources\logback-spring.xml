<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration>
<configuration>
	<include resource="org/springframework/boot/logging/logback/defaults.xml"/>
	<include resource="org/springframework/boot/logging/logback/console-appender.xml"/>
	<springProperty scope="context" name="springAppName" source="spring.application.name"/>
	<!--获取主机名-->
	<!--日志文件保存路径-->
	<property name="LOG_FILE_PATH" value="/data/log"/>
	<!--每天记录日志到文件appender-->
	<!--    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
	<!--        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">-->
	<!--            <fileNamePattern>${LOG_FILE_PATH}/${springAppName}-%d{yyyy-MM-dd}.log</fileNamePattern>-->
	<!--            <maxHistory>60</maxHistory>-->
	<!--            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">-->
	<!--                <maxFileSize>10MB</maxFileSize>-->
	<!--            </timeBasedFileNamingAndTriggeringPolicy>-->
	<!--        </rollingPolicy>-->
	<!--        <encoder>-->
	<!--            <pattern>${FILE_LOG_PATTERN}</pattern>-->
	<!--        </encoder>-->
	<!--    </appender>-->
	<appender name="LoghubAppender" class="com.tencentcloudapi.cls.LoghubAppender">
		<!--必选项-->
		<!--域名配置 详见https://cloud.tencent.com/document/product/614/18940#.E5.9F.9F.E5.90.8D-->
		<endpoint>ap-guangzhou.cls.tencentyun.com</endpoint>
		<!--密钥信息 前往https://console.cloud.tencent.com/cam/capi获取-->
		<accessKeyId>AKIDviJhqiKhww2rRmshIekZ7PQ005xd3C6o</accessKeyId>
		<accessKeySecret>d5zhdMuFNOCoCMHyRghU4VjjU0IJ5dx3</accessKeySecret>
		<!--日志主题ID-->
		<topicId>664d2483-0841-4f92-99df-baeffc9d86c2</topicId>


		<!-- 可选项 详见 '参数说明'-->
		<totalSizeInBytes>104857600</totalSizeInBytes>
		<maxBlockMs>0</maxBlockMs>
		<sendThreadCount>8</sendThreadCount>
		<batchSizeThresholdInBytes>524288</batchSizeThresholdInBytes>
		<batchCountThreshold>4096</batchCountThreshold>
		<lingerMs>2000</lingerMs>
		<retries>10</retries>
		<baseRetryBackoffMs>100</baseRetryBackoffMs>
		<maxRetryBackoffMs>50000</maxRetryBackoffMs>


		<!-- 可选项 设置时间格式 -->
		<timeFormat>yyyy-MM-dd'T'HH:mm:ssZ</timeFormat>
		<timeZone>Asia/Shanghai</timeZone>
		<encoder>
			<pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger - %msg</pattern>
		</encoder>
		<mdcFields>THREAD_ID,MDC_KEY</mdcFields>
	</appender>
	<root level="info">
		<appender-ref ref="CONSOLE"/>
		<!--        <appender-ref ref="FILE"/>-->
		<appender-ref ref="LoghubAppender"/>
	</root>
</configuration>

