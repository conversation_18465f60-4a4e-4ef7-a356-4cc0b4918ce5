package org.jeecg.modules.alumni.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.alumni.entity.Street;
import org.jeecg.modules.alumni.service.IStreetService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 查询省市区的街道
 * @Author: klb
 * @date 2023/10/12 19:36
 */
@RestController
@RequestMapping("/street")
@Api(tags = "街道API")
public class StreetsController {

    @Resource
    IStreetService streetService;

    @GetMapping("/getStreetByCode")
    @ApiOperation(value="查询街道", notes="查询区域下的街道")
    public Result<List<Street>> getStreetByCode(@RequestParam("areaCode") String areaCode) {
        LambdaQueryWrapper<Street> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Street::getAreaCode, areaCode);
        List<Street> list = this.streetService.list(queryWrapper);
        return Result.OK(list);
    }

}
