package org.jeecg.modules.alumni.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: alumni_activity
 * @Author: jeecg-boot
 * @Date:   2023-10-26
 * @Version: V1.0
 */
@Data
@TableName("alumni_activity")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="alumni_activity对象", description="alumni_activity")
public class AlumniActivity implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**活动编号*/
	@Excel(name = "活动编号", width = 15)
    @ApiModelProperty(value = "活动编号")
    private String activityNo;
	/**活动名称*/
	@Excel(name = "活动名称", width = 35)
    @ApiModelProperty(value = "活动名称")
    private String activityName;

    /**活动封面图片*/
    @ApiModelProperty(value = "活动封面图片")
    private String activityCoverImage;

    /**活动封面图片*/
    @ApiModelProperty(value = "活动封面图片url")
    private String activityCoverImageUrl;
	/**举办地址*/
	@Excel(name = "举办地址", width = 35)
    @ApiModelProperty(value = "举办地址")
    private String activityAddress;
	/**举办日期*/
	@Excel(name = "举办日期", width = 20, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "举办日期")
    private Date activityDate;
    /**活动结束时间*/
    @Excel(name = "活动结束时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "活动结束时间")
    private Date activityEndTime;
	/**报名截止时间*/
	@Excel(name = "报名截止时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "报名截止时间")
    private Date signInDeadline;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新人*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新人")
    private Date updateTime;
	/**活动说明*/
	@Excel(name = "活动说明", width = 15)
    @ApiModelProperty(value = "活动说明")
    private String activityRemark;
	/**是否AA制*/
	@Excel(name = "是否AA制", width = 15)
    @ApiModelProperty(value = "是否AA制")
    private String isDutch;
	/**分摊费用(元)*/
	@Excel(name = "分摊费用(元)", width = 15)
    @ApiModelProperty(value = "分摊费用(元)")
    private BigDecimal treatmentMoney;
	/**启用状态*/
	@Excel(name = "启用状态", width = 15)
    @ApiModelProperty(value = "启用状态")
    private String enable;
	/**删除状态*/
	@Excel(name = "删除状态", width = 15)
    @ApiModelProperty(value = "删除状态")
    private String isDel;

    @ApiModelProperty(value = "活动状态")
    @Dict(dicCode = "activity_status")
    private Integer status;

    @ApiModelProperty(value = "是否已报名活动")
    @TableField(exist = false)
    private Boolean hasApply;

    @ApiModelProperty(value = "报名时间")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private Date applyTime;
}
