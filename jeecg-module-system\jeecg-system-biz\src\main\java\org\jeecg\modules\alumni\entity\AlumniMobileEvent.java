package org.jeecg.modules.alumni.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: alumni_mobile_event
 * @Author: jeecg-boot
 * @Date:   2023-10-26
 * @Version: V1.0
 */
@Data
@TableName("alumni_mobile_event")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="alumni_mobile_event对象", description="alumni_mobile_event")
public class AlumniMobileEvent implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**userId*/
	@Excel(name = "userId", width = 15)
    @ApiModelProperty(value = "userId")
    private java.lang.String userId;
    /**时间动作*/
    @Excel(name = "所属模块", width = 15)
    @ApiModelProperty(value = "module")
    private java.lang.String module;
	/**页面路径*/
	@Excel(name = "页面路径", width = 15)
    @ApiModelProperty(value = "页面路径")
    private java.lang.String pageUrl;
	/**时间动作*/
	@Excel(name = "时间动作", width = 15)
    @ApiModelProperty(value = "时间动作")
    private java.lang.String action;
	/**事件名称*/
	@Excel(name = "事件名称", width = 15)
    @ApiModelProperty(value = "事件名称")
    private java.lang.String event;

    /**extra*/
    @Excel(name = "extra", width = 15)
    @ApiModelProperty(value = "extra")
    private java.lang.String extra;
	/**createTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "createTime")
    private java.util.Date createTime;
}
