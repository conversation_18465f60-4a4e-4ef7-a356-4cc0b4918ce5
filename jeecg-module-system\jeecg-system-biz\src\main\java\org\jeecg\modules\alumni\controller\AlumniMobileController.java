package org.jeecg.modules.alumni.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.alumni.entity.AlumniActivity;
import org.jeecg.modules.alumni.entity.AlumniMobileEvent;
import org.jeecg.modules.alumni.entity.AlumniUser;
import org.jeecg.modules.alumni.enums.ActivityConstants;
import org.jeecg.modules.alumni.service.IAlumniActivityService;
import org.jeecg.modules.alumni.service.IAlumniMobileEventService;
import org.jeecg.modules.alumni.service.IAlumniUserService;
import org.jeecg.modules.utils.SmsUtils;
import org.jeecg.modules.utils.SpellUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * 校友移动端.
 *
 * <AUTHOR> goku
 * @version : 1.0 2023/10/12
 * @company 深圳市超智慧信息科技有限公司
 * @organization hypersmart
 * @team 611
 * @since : 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/mobile")
@Api(tags = "校友移动端")
public class AlumniMobileController {
    @Autowired
    private IAlumniUserService alumniUserService;
    @Autowired
    private IAlumniActivityService alumniActivityService;

    @Autowired
    private IAlumniMobileEventService eventService;

    @Autowired
    private RedisUtil redisUtil;

    @ApiOperation(value = "获取手机验证码", notes = "获取手机验证码")
    @PostMapping(value = "/authCode")
    public Result<?> authCode(@RequestParam(value = "phone") String phone) {
        if (redisUtil.hasKey(phone)) {
            throw new JeecgBootException("请在1分钟后重试");
        }
        String authCode = SmsUtils.sendAuthCode(phone);
        if (StringUtils.isEmpty(authCode)) {
            throw new JeecgBootException("验证码发送失败");
        }
        redisUtil.set(phone, authCode);
        redisUtil.expire(phone, 60);
        return Result.ok(authCode);
    }

    @ApiOperation(value = "成员数量", notes = "校友用户-成员数量")
    @PostMapping(value = "/memberNum")
    public Result<?> memberNum() {
        Long userCount = alumniUserService.memberCount();
        return Result.ok(userCount);
    }

    @ApiOperation(value = "登录", notes = "校友用户-登录")
    @PostMapping(value = "/login")
    public Result<?> login(@RequestParam(value = "phone") String phone, @RequestParam(value = "authCode") String authCode) {
        if (!redisUtil.hasKey(phone)) {
            throw new JeecgBootException("验证码不正确");
        }
        String cacheCode = redisUtil.get(phone).toString();
        if (!StringUtils.equals(authCode, cacheCode)) {
            throw new JeecgBootException("验证码不正确");
        }
        AlumniUser user = alumniUserService.queryByMobile(phone);
        return Result.ok(user);
    }

    @ApiOperation(value = "校友用户-修改", notes = "校友用户-修改信息")
    @PostMapping(value = "/modify")
    public Result<AlumniUser> modify(@RequestBody AlumniUser userInfo, HttpServletRequest req) {
        AlumniUser user = alumniUserService.getById(userInfo.getId());
        if (Objects.isNull(user)) {
            throw new JeecgBootException("用户不存在");
        }
        if(StringUtils.isNotEmpty(userInfo.getUserName())){
            userInfo.setPinyin(SpellUtils.getAllPinyin(userInfo.getUserName()));
        }
        alumniUserService.updateById(userInfo);
        return Result.OK();
    }

    @ApiOperation(value = "校友用户-事件上报", notes = "校友用户-事件上报")
    @PostMapping(value = "/eventReport")
    public Result<?> eventReport(@RequestBody AlumniMobileEvent event) {
        eventService.save(event);
        return Result.OK();
    }

    @ApiOperation(value = "校友用户-注册", notes = "校友用户-注册校友")
    @PostMapping(value = "/register")
    public Result<AlumniUser> register(@RequestBody AlumniUser userInfo, HttpServletRequest req) {
        AlumniUser user = alumniUserService.queryByMobile(userInfo.getPhone());
        if (!Objects.isNull(user)) {
            throw new JeecgBootException("手机号已被注册");
        }
        if(StringUtils.isNotEmpty(userInfo.getUserName())){
            userInfo.setPinyin(SpellUtils.getAllPinyin(userInfo.getUserName()));
        }
        alumniUserService.save(userInfo);
        return Result.OK(userInfo);
    }

    @ApiOperation(value = "校友用户-获取当前校友用户", notes = "校友用户-获取当前校友用户")
    @GetMapping(value = "/queryUserInfo")
    public Result<AlumniUser> queryUserInfo(@RequestParam(value = "userId") String userId) {
        AlumniUser user = alumniUserService.getById(userId);
        if (Objects.isNull(user)) {
            throw new JeecgBootException("用户不存在");
        }
        return Result.OK(user);
    }

    @ApiOperation(value = "校友用户-根据手机号获取当前校友用户", notes = "校友用户-根据手机号获取当前校友用户")
    @GetMapping(value = "/queryByMobile")
    public Result<AlumniUser> queryByMobile(@RequestParam(value = "phone") String phone) {
        AlumniUser user = alumniUserService.queryByMobile(phone);
        if (Objects.isNull(user)) {
            throw new JeecgBootException("用户不存在");
        }
        return Result.OK(user);
    }


    @ApiOperation(value="报名活动", notes="报名活动")
    @PostMapping("/apply")
    public Result<?> applyActivity(@RequestParam(name="activityId",required = true)String activityId,@RequestParam(name="userId",required = true) String userId)throws Exception{
        alumniActivityService.applyActivity(activityId,userId);
        return Result.ok();
    }

    @ApiOperation(value="报名活动详情", notes="报名活动详情")
    @PostMapping("/activityDetail")
    public Result<?> queryActivity(@RequestParam(name="activityId",required = true)String activityId,@RequestParam(name="userId",required = true) String userId)throws Exception{
        AlumniActivity activity = alumniActivityService.userActivity(activityId, userId);
        return Result.ok(activity);
    }

    @ApiOperation(value="校友活动-分页列表查询(带报名状态)", notes="校友活动-分页列表查询(带报名状态)")
    @GetMapping(value = "/myActivityList")
    public Result<IPage<AlumniActivity>> myList(AlumniActivity alumniActivity,
                                                @RequestParam(name="userId") String userId,
                                                @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                HttpServletRequest req) {
        QueryWrapper<AlumniActivity> queryWrapper = QueryGenerator.initQueryWrapper(alumniActivity, req.getParameterMap());
        Page<AlumniActivity> page = new Page<AlumniActivity>(pageNo, pageSize);
        if(Objects.isNull(alumniActivity.getEnable())){
            queryWrapper.eq("enable", ActivityConstants.ENABLE);
        }
        if(Objects.isNull(alumniActivity.getIsDel())){
            queryWrapper.eq("is_del", CommonConstant.DEL_FLAG_0);
        }
        queryWrapper.orderByDesc("status");
        queryWrapper.orderByDesc("activity_end_time");
        IPage<AlumniActivity> pageList = alumniActivityService.page(page, queryWrapper);
        List<AlumniActivity> myActivityList=alumniActivityService.userApplyActivityList(userId,null);
        if(Objects.nonNull(pageList)){
            List<AlumniActivity> activities=pageList.getRecords();
            if(CollectionUtil.isNotEmpty(activities)){
                for(AlumniActivity activity:activities){
                    myActivityList.stream()
                            .filter(t->t.getId().equals(activity.getId()))
                            .findFirst().ifPresent(t-> {
                                activity.setHasApply(true);
                                activity.setApplyTime(t.getApplyTime());
                            });
                }
            }
        }
        return Result.OK(pageList);
    }

    @ApiOperation(value = "校友活动-可报名的活动条数", notes = "校友活动-可报名的活动条数")
    @GetMapping(value = "/effectCount")
    public Result<Long> effectiveCount() {
        return Result.ok(alumniActivityService.effectiveCount());
    }
}
