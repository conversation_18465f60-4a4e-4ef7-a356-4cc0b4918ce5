package org.jeecg.modules.alumni.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.alumni.entity.AlumniUser;
import org.jeecg.modules.alumni.entity.AlumniUserFriendRef;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.alumni.vo.AlumniUserVo;

/**
 * @Description: alumni_user_friend_ref
 * @Author: jeecg-boot
 * @Date:   2023-11-06
 * @Version: V1.0
 */
public interface IAlumniUserFriendRefService extends IService<AlumniUserFriendRef> {
    boolean isFriend(String userId,String friendId);

    Page<AlumniUser> queryMyFriendPage(Page<AlumniUser> page, String userId,String userName);
    Page<AlumniUserVo> queryMemberPage(String userId,String userName,Integer pageNo,Integer pageSize);
}
