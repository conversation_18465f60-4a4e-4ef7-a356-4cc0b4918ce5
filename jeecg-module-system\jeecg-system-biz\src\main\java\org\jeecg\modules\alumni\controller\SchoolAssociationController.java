package org.jeecg.modules.alumni.controller;

import cn.hutool.extra.servlet.ServletUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.alumni.dto.SchoolAssociationInfo;
import org.jeecg.modules.alumni.entity.AlumniUser;
import org.jeecg.modules.alumni.service.IAlumniUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/school")
@Api(tags="学校相关API")
public class SchoolAssociationController {

    @Autowired
    private IAlumniUserService alumniUserService;

    @ApiOperation(value="校友会-获取主会信息", notes="校友用户-获取当前校友用户")
    @GetMapping(value = "/querySchoolMajorAssociation")
    public Result<SchoolAssociationInfo> querySchoolMajorAssociation(HttpServletRequest req) {
        Cookie tokenCookie= ServletUtil.getCookie(req,"__token__");
        return Result.OK(null);
    }
}
