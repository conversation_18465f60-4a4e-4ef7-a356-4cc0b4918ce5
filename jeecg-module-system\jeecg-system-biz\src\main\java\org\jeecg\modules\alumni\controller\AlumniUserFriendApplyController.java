package org.jeecg.modules.alumni.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.alumni.entity.AlumniUserFriendApply;
import org.jeecg.modules.alumni.service.IAlumniUserFriendApplyService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: alumni_user_friend_apply
 * @Author: jeecg-boot
 * @Date:   2023-11-06
 * @Version: V1.0
 */
@Api(tags="alumni_user_friend_apply")
@RestController
@RequestMapping("/alumni/alumniUserFriendApply")
@Slf4j
public class AlumniUserFriendApplyController extends JeecgController<AlumniUserFriendApply, IAlumniUserFriendApplyService> {
	@Autowired
	private IAlumniUserFriendApplyService alumniUserFriendApplyService;
	
	/**
	 * 分页列表查询
	 *
	 * @param alumniUserFriendApply
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "alumni_user_friend_apply-分页列表查询")
	@ApiOperation(value="alumni_user_friend_apply-分页列表查询", notes="alumni_user_friend_apply-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<AlumniUserFriendApply>> queryPageList(AlumniUserFriendApply alumniUserFriendApply,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AlumniUserFriendApply> queryWrapper = QueryGenerator.initQueryWrapper(alumniUserFriendApply, req.getParameterMap());
		Page<AlumniUserFriendApply> page = new Page<AlumniUserFriendApply>(pageNo, pageSize);
		IPage<AlumniUserFriendApply> pageList = alumniUserFriendApplyService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param alumniUserFriendApply
	 * @return
	 */
	@AutoLog(value = "alumni_user_friend_apply-添加")
	@ApiOperation(value="alumni_user_friend_apply-添加", notes="alumni_user_friend_apply-添加")
	@RequiresPermissions("alumni:alumni_user_friend_apply:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody AlumniUserFriendApply alumniUserFriendApply) {
		alumniUserFriendApplyService.save(alumniUserFriendApply);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param alumniUserFriendApply
	 * @return
	 */
	@AutoLog(value = "alumni_user_friend_apply-编辑")
	@ApiOperation(value="alumni_user_friend_apply-编辑", notes="alumni_user_friend_apply-编辑")
	@RequiresPermissions("alumni:alumni_user_friend_apply:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody AlumniUserFriendApply alumniUserFriendApply) {
		alumniUserFriendApplyService.updateById(alumniUserFriendApply);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "alumni_user_friend_apply-通过id删除")
	@ApiOperation(value="alumni_user_friend_apply-通过id删除", notes="alumni_user_friend_apply-通过id删除")
	@RequiresPermissions("alumni:alumni_user_friend_apply:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		alumniUserFriendApplyService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "alumni_user_friend_apply-批量删除")
	@ApiOperation(value="alumni_user_friend_apply-批量删除", notes="alumni_user_friend_apply-批量删除")
	@RequiresPermissions("alumni:alumni_user_friend_apply:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.alumniUserFriendApplyService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "alumni_user_friend_apply-通过id查询")
	@ApiOperation(value="alumni_user_friend_apply-通过id查询", notes="alumni_user_friend_apply-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<AlumniUserFriendApply> queryById(@RequestParam(name="id",required=true) String id) {
		AlumniUserFriendApply alumniUserFriendApply = alumniUserFriendApplyService.getById(id);
		if(alumniUserFriendApply==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(alumniUserFriendApply);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param alumniUserFriendApply
    */
    @RequiresPermissions("alumni:alumni_user_friend_apply:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AlumniUserFriendApply alumniUserFriendApply) {
        return super.exportXls(request, alumniUserFriendApply, AlumniUserFriendApply.class, "alumni_user_friend_apply");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("alumni:alumni_user_friend_apply:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AlumniUserFriendApply.class);
    }

}
