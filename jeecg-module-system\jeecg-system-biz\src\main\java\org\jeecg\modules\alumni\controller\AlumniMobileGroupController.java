package org.jeecg.modules.alumni.controller;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBoot500Exception;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.modules.alumni.entity.AlumniUser;
import org.jeecg.modules.alumni.service.IAlumniUserFriendRefService;
import org.jeecg.modules.alumni.service.IAlumniUserService;
import org.jeecg.modules.utils.SpellUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags="移动端群组")
@RestController
@RequestMapping("/mobile/group")
public class AlumniMobileGroupController {

    @Resource
    IAlumniUserFriendRefService friendRefService;
    @Resource
    IAlumniUserService userService;

    @ApiOperation(value = "校友详情", notes = "校友详情")
    @PostMapping(value = "/friendDetail")
    public Result<?> friendDetail(@RequestParam(value = "userId") String userId, @RequestParam(value = "friendId") String friendId) {
        //根据id查询userId的校友信息
        AlumniUser user = userService.getById(userId);
        if(ObjectUtils.isEmpty(user)){
            throw new JeecgBootException("用户不存在");
        }
        //判断是否是好友
        if(!friendRefService.isFriend(userId,friendId)){
            //不是好友，隐藏好友姓名，手机号信息
           userService.conceal(user);
        }
        return Result.ok(user);
    }

    @ApiOperation(value = "群组成员", notes = "群组成员")
    @PostMapping(value = "/groupMemberList")
    public Result<?> list(@RequestParam(value = "userId") String userId,
                          @RequestParam(value = "groupId",required = false) String groupId,
                          @RequestParam(value = "userName",required = false) String userName,
                          @RequestParam(value = "pageNo") Integer pageNo,
                          @RequestParam(value = "pageSize") Integer pageSize) {
        return Result.ok(friendRefService.queryMemberPage(userId,userName,pageNo,pageSize));
    }
}
