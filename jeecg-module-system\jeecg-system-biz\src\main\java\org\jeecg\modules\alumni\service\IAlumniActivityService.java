package org.jeecg.modules.alumni.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.alumni.entity.AlumniActivity;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.alumni.vo.AlumniActivityApplyVo;

import java.util.List;

/**
 * @Description: alumni_activity
 * @Author: jeecg-boot
 * @Date:   2023-10-26
 * @Version: V1.0
 */
public interface IAlumniActivityService extends IService<AlumniActivity> {
    String queryMaxActivityNo();

    void applyActivity(String activityId,String userId)throws Exception;

    AlumniActivity userActivity(String activityId,String userId);

    List<AlumniActivity> userApplyActivityList(String userId, Integer status);
    IPage<AlumniActivity> userApplyActivityPage(String userId, Integer status,Integer pageNo,Integer pageSize);

    IPage<AlumniActivityApplyVo> applyUserPage(String activityId, Integer pageNo, Integer pageSize);

    Long effectiveCount();

    void activityJob();
}
