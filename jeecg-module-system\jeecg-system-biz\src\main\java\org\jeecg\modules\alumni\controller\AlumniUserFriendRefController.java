package org.jeecg.modules.alumni.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.alumni.entity.AlumniUserFriendRef;
import org.jeecg.modules.alumni.service.IAlumniUserFriendRefService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: alumni_user_friend_ref
 * @Author: jeecg-boot
 * @Date:   2023-11-06
 * @Version: V1.0
 */
@Api(tags="alumni_user_friend_ref")
@RestController
@RequestMapping("/alumni/alumniUserFriendRef")
@Slf4j
public class AlumniUserFriendRefController extends JeecgController<AlumniUserFriendRef, IAlumniUserFriendRefService> {
	@Autowired
	private IAlumniUserFriendRefService alumniUserFriendRefService;
	
	/**
	 * 分页列表查询
	 *
	 * @param alumniUserFriendRef
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "alumni_user_friend_ref-分页列表查询")
	@ApiOperation(value="alumni_user_friend_ref-分页列表查询", notes="alumni_user_friend_ref-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<AlumniUserFriendRef>> queryPageList(AlumniUserFriendRef alumniUserFriendRef,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AlumniUserFriendRef> queryWrapper = QueryGenerator.initQueryWrapper(alumniUserFriendRef, req.getParameterMap());
		Page<AlumniUserFriendRef> page = new Page<AlumniUserFriendRef>(pageNo, pageSize);
		IPage<AlumniUserFriendRef> pageList = alumniUserFriendRefService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param alumniUserFriendRef
	 * @return
	 */
	@AutoLog(value = "alumni_user_friend_ref-添加")
	@ApiOperation(value="alumni_user_friend_ref-添加", notes="alumni_user_friend_ref-添加")
	@RequiresPermissions("alumni:alumni_user_friend_ref:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody AlumniUserFriendRef alumniUserFriendRef) {
		alumniUserFriendRefService.save(alumniUserFriendRef);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param alumniUserFriendRef
	 * @return
	 */
	@AutoLog(value = "alumni_user_friend_ref-编辑")
	@ApiOperation(value="alumni_user_friend_ref-编辑", notes="alumni_user_friend_ref-编辑")
	@RequiresPermissions("alumni:alumni_user_friend_ref:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody AlumniUserFriendRef alumniUserFriendRef) {
		alumniUserFriendRefService.updateById(alumniUserFriendRef);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "alumni_user_friend_ref-通过id删除")
	@ApiOperation(value="alumni_user_friend_ref-通过id删除", notes="alumni_user_friend_ref-通过id删除")
	@RequiresPermissions("alumni:alumni_user_friend_ref:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		alumniUserFriendRefService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "alumni_user_friend_ref-批量删除")
	@ApiOperation(value="alumni_user_friend_ref-批量删除", notes="alumni_user_friend_ref-批量删除")
	@RequiresPermissions("alumni:alumni_user_friend_ref:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.alumniUserFriendRefService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "alumni_user_friend_ref-通过id查询")
	@ApiOperation(value="alumni_user_friend_ref-通过id查询", notes="alumni_user_friend_ref-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<AlumniUserFriendRef> queryById(@RequestParam(name="id",required=true) String id) {
		AlumniUserFriendRef alumniUserFriendRef = alumniUserFriendRefService.getById(id);
		if(alumniUserFriendRef==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(alumniUserFriendRef);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param alumniUserFriendRef
    */
    @RequiresPermissions("alumni:alumni_user_friend_ref:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AlumniUserFriendRef alumniUserFriendRef) {
        return super.exportXls(request, alumniUserFriendRef, AlumniUserFriendRef.class, "alumni_user_friend_ref");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("alumni:alumni_user_friend_ref:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AlumniUserFriendRef.class);
    }

}
