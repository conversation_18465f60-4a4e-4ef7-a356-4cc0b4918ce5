<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.alumni.mapper.AlumniUserFriendRefMapper">

    <!-- 查询我的好友-->
    <select id="queryMyFriendList" resultType="org.jeecg.modules.alumni.entity.AlumniUser">
        select u.* from alumni_user_friend_ref ref
                 left join alumni_user u
                     on ref.friend_id = u.id
                 where user_id = #{userId}
        <if test="userName!=null and userName!=''">
            and  (u.user_name like CONCAT('%',#{userName},'%') or u.phone like CONCAT('%',#{userName},'%') )
        </if>
        order by u.pinyin
    </select>

    <select id="queryMyFriendPage" resultType="org.jeecg.modules.alumni.entity.AlumniUser">
        select u.* from alumni_user_friend_ref ref
                            left join alumni_user u
                                      on ref.friend_id = u.id
        where user_id = #{userId}
        <if test="userName!=null and userName!=''">
            and  (u.user_name like CONCAT('%',#{userName},'%') or u.phone like CONCAT('%',#{userName},'%') )
        </if>
        order by u.pinyin
    </select>

    <select id="queryMemberPage" resultType="org.jeecg.modules.alumni.vo.AlumniUserVo">
        select u.*,
               (select count(1)
                from alumni_user_friend_ref
                where friend_id = u.id
                and user_id=#{userId}
                ) as is_friend,
                (select count(1)
                from alumni_user_friend_apply
                where friend_id = u.id
                and user_id=#{userId}
                ) as is_apply
        from alumni_user u
        <if test="userName!=null and userName!=''">
          where  u.user_name like CONCAT('%',#{userName},'%')
        </if>
        order by u.pinyin
    </select>

</mapper>