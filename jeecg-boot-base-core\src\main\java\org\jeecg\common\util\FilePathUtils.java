package org.jeecg.common.util;

/**
 * 文件路径tool.
 *
 * <AUTHOR> goku
 * @version : 1.0 2023/09/05
 * @company 深圳市超智慧信息科技有限公司
 * @organization hypersmart
 * @team 611
 * @since : 1.0
 **/
public class FilePathUtils {

    public static String getPathExtension(String filePath){
        String fileExtension = "";

        int dotIndex = filePath.lastIndexOf(".");
        if (dotIndex > 0 && dotIndex < filePath.length() - 1) {
            fileExtension = filePath.substring(dotIndex + 1);
        }
        return fileExtension;
    }
}
