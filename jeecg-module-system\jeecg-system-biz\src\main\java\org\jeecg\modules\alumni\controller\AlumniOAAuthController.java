package org.jeecg.modules.alumni.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.alumni.dto.AccessTokenResp;
import org.jeecg.modules.alumni.entity.AlumniUser;
import org.jeecg.modules.alumni.service.IAlumniUserService;
import org.jeecg.modules.system.entity.CheckInUserMonthSalary;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Controller
@RequestMapping("/oaAuth")
@Api(tags="公众号授权相关")
public class AlumniOAAuthController {

    @Autowired
    private IAlumniUserService alumniUserService;
    @Value("${alumni.oa.appid:}")
    private String appId;

    @Value("${alumni.oa.secret:}")
    private String secret;

    private String authUrlTemplate="https://open.weixin.qq.com/connect/oauth2/authorize?appid={APPID}&redirect_uri={REDIRECT_URI}&response_type=code&scope=snsapi_base&state={STATE}#wechat_redirect";

    @Value("${alumni.oa.auth-callback-url:}")
    private String authCallbackUrl="http://localhost:30880/alumni/oaAuth/authCallback";

    private String accessTokenUrl="https://api.weixin.qq.com/sns/oauth2/access_token?appid={APPID}&secret={SECRET}&code={CODE}&grant_type=authorization_code";

    @Value("${alumni.oa.auth-success-url:}")
    private String authSuccessUrl="http://localhost:30880/alumni/demo1.html";

    @ApiOperation(value="公众号-获取授权跳转地址", notes="公众号-获取授权跳转地址")
    @GetMapping(value = "/authUrl")
    public @ResponseBody Result<String> authUrl(HttpServletRequest req) {
        Map<String,String> param=new HashMap<>();
        param.put("APPID",this.appId);
        param.put("REDIRECT_URI", URLUtil.encodeAll(this.authCallbackUrl));
        param.put("STATE","fakeState");
        String authUrl=StrUtil.format(authUrlTemplate,param);
        return Result.OK(authUrl);
    }

    @ApiOperation(value="公众号-获取授权回调处理", notes="公众号-获取授权回调处理")
    @RequestMapping(value = "/authCallback")
    public String authCallback(@RequestParam(name = "code") String code, @RequestParam(name = "state") String state, HttpServletRequest req, HttpServletResponse response) {
        Map<String,String> param=new HashMap<>();
        param.put("APPID",appId);
        param.put("SECRET",secret);
        param.put("CODE",code);
        String accessToken=StrUtil.format(accessTokenUrl,param);
        String resp=HttpUtil.get(accessToken);
        AccessTokenResp accessTokenResp= JSON.parseObject(resp,AccessTokenResp.class);
        if(StrUtil.isEmpty(accessTokenResp.getOpenid())){
            return "oa/redirect";
        }
        AlumniUser alumniUser=alumniUserService.checkOrInitUserByOpenId(accessTokenResp.getOpenid());
        //基于openId，静默初始用户信息，创建回话
        String sessionToken=alumniUser.getOpenId();
        Cookie cookie=new Cookie("__token__",sessionToken);
        cookie.setMaxAge(24*60*60);
        cookie.setPath("/");
//        cookie.setDomain(".hypersmart.ltd");
//        cookie.setDomain(".localhost:38080");
        ServletUtil.addCookie(response,cookie);
        return "oa/redirect";
    }

    @GetMapping("/notify")
    public  @ResponseBody String check(HttpServletRequest request){
        System.out.println("get方法");
        String signature = request.getParameter("signature");
        String timestamp = request.getParameter("timestamp");
        String nonce = request.getParameter("nonce");
        String echostr = request.getParameter("echostr");
//        if (WechatUtils.checkSignature(signature, timestamp, nonce)) {
            System.out.println("检验通过");
            return echostr;
//        }
//        System.out.println("检验不通过");
//        return "校验不通过";
    }
    @ApiOperation(value="公众号-获取授权回调处理", notes="公众号-获取授权回调处理")
    @PostMapping(value = "/notify")
    public @ResponseBody String notify(@RequestBody(required = false)  String body, HttpServletRequest req, HttpServletResponse response) {
        String token="lxyzweixin";
        String EncodingAESKey="SwkWPKnrlofnG9Olfx1lbX196dE9SJb5N0BwIRqBaHG";
        log.info(body);
        String url1="https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid="+"wx24ea535ee769fe80"+"&secret="+"9ad5c1e2bb370f373647d5786ceb54ab";
        String resp1=HttpUtil.get(url1);
        log.info(resp1);
        JSONObject resp1Json=JSON.parseObject(resp1);
        String access_token=resp1Json.getString("access_token");


        return "success";
    }

    @ApiOperation(value="公众号-获取授权回调处理", notes="公众号-获取授权回调处理")
    @RequestMapping(value = "/configMenu")
    public @ResponseBody String configMenu( HttpServletRequest req, HttpServletResponse response) {
        log.info("配置菜单");
//        String url1="https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={APPID}&secret={APPSECRET}";
//        Map<String,String> param=new HashMap<>();
//        param.put("APPID",appId);
//        param.put("APPSECRET",appId);
        String url1="https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid="+"wx24ea535ee769fe80"+"&secret="+"9ad5c1e2bb370f373647d5786ceb54ab";
        String resp1=HttpUtil.get(url1);
        log.info(resp1);
        JSONObject resp1Json=JSON.parseObject(resp1);
        String access_token=resp1Json.getString("access_token");
        String jsonBody="{\n" +
                "  \"button\": [\n" +
                "    {\n" +
                "      \"type\": \"click\",\n" +
                "      \"name\": \"校友会\",\n" +
                "      \"key\": \"menu1\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        String resp2=HttpRequest.post(" https://api.weixin.qq.com/cgi-bin/menu/create?access_token="+access_token).body(jsonBody).execute().body();
        return resp1+"-------"+resp2;
    }


    public static void main(String[] args){
       String r= HttpUtil.get("www.baidu.com");
       System.out.println(r);
    }


}
