-- `alumni-association`.alumni_user definition

CREATE TABLE `alumni_user` (
                               `id` varchar(64) NOT NULL,
                               `open_id` varchar(100) DEFAULT NULL,
                               `user_name` varchar(100) DEFAULT NULL,
                               `sex` varchar(100) DEFAULT NULL COMMENT '男/女',
                               `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
                               `birth` date DEFAULT NULL COMMENT '出生年月',
                               `graduate_year` int(11) DEFAULT NULL COMMENT '毕业年份',
                               `class_no` varchar(100) DEFAULT NULL,
                                `province` varchar(100) DEFAULT NULL COMMENT '省名称',
                               `city` varchar(100) DEFAULT NULL COMMENT '市名称',
                               `region` varchar(100) DEFAULT NULL COMMENT '区名称',
                               `street` varchar(256) DEFAULT NULL COMMENT '当前所在街道/乡镇',
                               `province_code` varchar(100) DEFAULT NULL COMMENT '省编码',
                               `city_code` varchar(100) DEFAULT NULL COMMENT '市编码',
                               `region_code` varchar(100) DEFAULT NULL COMMENT '区编码',
                               `street_code` varchar(256) DEFAULT NULL COMMENT '当前所在街道/乡镇编码',
                               `colleague` varchar(200) DEFAULT NULL COMMENT '大学名称',
                               `highest_education` varchar(100) DEFAULT NULL COMMENT '最高学历',
                               `company` varchar(200) DEFAULT NULL COMMENT '工作单位',
                               `post` varchar(200) DEFAULT NULL COMMENT '职位',
                               `company_province` varchar(200) DEFAULT NULL COMMENT '工作地址',
                               `company_city` varchar(100) DEFAULT NULL,
                               `company_region` varchar(100) DEFAULT NULL,
                               `company_street` varchar(100) DEFAULT NULL,
                               `company_address` varchar(100) DEFAULT NULL,
                               `company_province_code` varchar(200) DEFAULT NULL COMMENT '工作地址',
                               `company_city_code` varchar(100) DEFAULT NULL,
                               `company_region_code` varchar(100) DEFAULT NULL,
                               `company_street_code` varchar(100) DEFAULT NULL,
                               `hometown_province` varchar(100) DEFAULT NULL,
                               `hometown_city` varchar(100) DEFAULT NULL,
                               `hometown_region` varchar(100) DEFAULT NULL,
                               `hometown_street` varchar(100) DEFAULT NULL,
                               `hometown_province_code` varchar(100) DEFAULT NULL,
                               `hometown_city_code` varchar(100) DEFAULT NULL,
                               `hometown_region_code` varchar(100) DEFAULT NULL,
                               `hometown_street_code` varchar(100) DEFAULT NULL,
                               `hometown_address` varchar(100) DEFAULT NULL,
                               `present` varchar(100) DEFAULT NULL COMMENT '是否现场参加11/18校友会成立大会 是/否',
                               `advice` varchar(1024) DEFAULT NULL COMMENT '其他建议',
                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


drop table if exists alumni_activity;
create table if not exists alumni_activity
(
    id                       varchar(64)                not null comment '主键'
        primary key,
    activity_name            varchar(64)                not null comment '活动名称',
    activity_address         text                       null comment '举办地址',
    activity_date            datetime                   null comment '举办日期',
    sign_in_deadline         datetime                   null comment '报名截止时间',
    activity_no              varchar(10)                not null comment '活动编号',
    create_by                varchar(20)                null comment '创建人',
    create_time              datetime                   not null comment '创建时间',
    update_by                varchar(20)                null comment '更新人',
    update_time              datetime                   null comment '更新人',
    activity_remark          text                       null comment '活动说明',
    is_dutch                 varchar(10)                not null comment '是否AA制',
    treatment_money          decimal(8, 2) default 0.00 not null comment '分摊费用(元)',
    enable                   varchar(10)   default '1'  not null comment '启用状态',
    is_del                   varchar(10)   default '0'  not null comment '删除状态',
    status                   int(10)       default 0    null comment '活动状态',
    activity_end_time        datetime                   null comment '活动结束时间',
    activity_cover_image     text                       null comment '活动封面图片',
    activity_cover_image_url text                       null comment '封面图片url'
)
    comment '活动';

drop table if exists alumni_activity_user_apply;
create table if not exists alumni_activity_user_apply
(
    id          varchar(64)                        not null
        primary key,
    activity_id varchar(64)                        not null,
    user_id     varchar(64)                        not null,
    apply_time  datetime default CURRENT_TIMESTAMP not null comment '报名时间',
    create_time datetime default CURRENT_TIMESTAMP not null,
    constraint alumni_activity_user_apply_alumni_activity_id_fk
        foreign key (activity_id) references alumni_activity (id),
    constraint alumni_activity_user_apply_alumni_user_id_fk
        foreign key (user_id) references alumni_user (id)
)
    comment '活动报名';

drop table if exists alumni_user_friend_apply;

create table if not exists alumni_user_friend_apply
(
    id          varchar(64)                        not null
    primary key,
    friend_id varchar(64)                        not null comment '好友id',
    user_id     varchar(64)                        not null comment '申请人id',
    apply_time  datetime default CURRENT_TIMESTAMP not null comment '申请时间',
    status tinyint  default 0 comment '-1 拒绝  0 申请中 1.通过',
    approve_time  datetime null comment '审批时间',
    create_by varchar(20) null comment '创建人',
    create_time datetime default CURRENT_TIMESTAMP not null,
    update_time datetime null,
    update_by varchar(20) null,
    flag tinyint not null default 0,
    constraint alumni_user_friend_apply_friend_fk
    foreign key (friend_id) references alumni_user (id),
    constraint alumni_user_friend_apply_id_fk
    foreign key (user_id) references alumni_user (id)
    )
    comment '好友申请';


drop table if exists alumni_user_friend_ref;
create table if not exists alumni_user_friend_ref(
    id varchar(64) not null primary key ,
    user_id varchar(64) not null comment '用户id',
    friend_id varchar(64) not null comment '好友id',
    create_by varchar(20) null comment '创建人',
    create_time datetime default CURRENT_TIMESTAMP not null,
    update_time datetime null,
    update_by varchar(20) null     ,
    constraint alumni_user_friend_friend_fk
    foreign key (friend_id) references alumni_user (id),
    constraint alumni_user_friend_id_fk
    foreign key (user_id) references alumni_user (id)
    ) comment '好友关系';
