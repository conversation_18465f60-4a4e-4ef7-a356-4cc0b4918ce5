package org.jeecg.common.util.tencent;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.http.HttpProtocol;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.region.Region;
import org.jeecg.common.enums.CardFaceEnum;
import org.jeecg.common.util.FilePathUtils;
import org.jeecg.config.tencentcloud.TencentCloudConfig;
import org.jeecg.config.tencentcloud.TencentCloudCosConfig;

import java.io.File;
import java.util.UUID;

/**
 * cos.
 *
 * <AUTHOR> goku
 * @version : 1.0 2023/09/05
 * @company 深圳市超智慧信息科技有限公司
 * @organization hypersmart
 * @team 611
 * @since : 1.0
 **/
public class TencentCloudCosUtils {
    public static String uploadCosFile(String filePath, String cosFileName, TencentCloudConfig config) throws Exception {
        String secretId = config.getSecretId();//用户的 SecretId，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参见 https://cloud.tencent.com/document/product/598/37140
        String secretKey = config.getSecretKey();//用户的 SecretKey，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参见 https://cloud.tencent.com/document/product/598/37140
        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        Region region = new Region(config.getRegion());
        ClientConfig clientConfig = new ClientConfig(region);
        // 这里建议设置使用 https 协议
        // 从 5.6.54 版本开始，默认使用了 https
        clientConfig.setHttpProtocol(HttpProtocol.https);
        // 3 生成 cos 客户端。
        COSClient cosClient = new COSClient(cred, clientConfig);
        // 指定要上传的文件
        File localFile = new File(filePath);
//        if(!localFile.exists()){
//            return null;
//        }
        // 指定文件将要存放的存储桶
        String bucketName = config.getCos().getBucketName();
        // 指定文件上传到 COS 上的路径，即对象键。例如对象键为 folder/picture.jpg，则表示将文件 picture.jpg 上传到 folder 路径下
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, cosFileName, localFile);
        cosClient.putObject(putObjectRequest);
        return getCosUrl(cosFileName,config.getCos());
    }


    /**
     * 上传身份证
     *
     * @param filePath
     * @param config
     * @return
     * @throws Exception
     */
    public static String uploadIdCardCosFile(CardFaceEnum cardFaceEnum, String filePath, TencentCloudConfig config) throws Exception {
        TencentCloudCosConfig cosConfig = config.getCos();
        String extension = FilePathUtils.getPathExtension(filePath);
        String cosFileName = cosConfig.getIdCardPath() + UUID.randomUUID() +"_"+ cardFaceEnum.ordinal() +"."+ extension;
        return uploadCosFile(filePath, cosFileName, config);
    }

    /**
     * 上传身份证
     *
     * @param sourceFilePath
     * @param destCosFileName
     * @param config
     * @return
     * @throws Exception
     */
    public static String uploadLocalFile(String sourceFilePath,String destCosFileName, TencentCloudConfig config) throws Exception {
        TencentCloudCosConfig cosConfig = config.getCos();
        String cosFileName = cosConfig.getPath() + destCosFileName;
        return uploadCosFile(sourceFilePath, cosFileName, config);
    }

    /**
     * 上传身份证
     *
     * @param filePath
     * @param config
     * @return
     * @throws Exception
     */
    public static String uploadBankCardCosFile( CardFaceEnum cardFaceEnum, String filePath, TencentCloudConfig config) throws Exception {
        TencentCloudCosConfig cosConfig = config.getCos();
        String extension = FilePathUtils.getPathExtension(filePath);
        String cosFileName = cosConfig.getBankCardPath() + UUID.randomUUID() +"_"+ cardFaceEnum.ordinal() +"."+ extension;
        return uploadCosFile(filePath, cosFileName, config);
    }

    private static String getCosUrl(String cosFileName,TencentCloudCosConfig config){
        return "https://"+config.getBucketName()+config.getUrl()+cosFileName;
    }
}

