#人员银行卡信息
drop table sys_user_bank_card;
create table sys_user_bank_card
(
    id                 varchar(64) primary key comment '主键',
    id_card            varchar(64)  not null comment '身份证号码',
    card_front_pic     varchar(200) null comment '银行卡正面图片',
    card_back_pic      varchar(200) null comment '银行卡反面图片',
    bank_card_code     varchar(64)  not null comment '银行卡号',
    bank_open_name     varchar(64)  not null comment '开户行地址',
    bank_open_province varchar(20)  not null comment '开户行省份',
    bank_open_city     varchar(20)  not null comment '开户行城市',
    is_master          tinyint      not null default 1 comment '是否是主卡 1 是 0 否',
    create_time        datetime     not null comment '创建时间',
    update_time        datetime     null comment '更新时间',
    create_by          varchar(64)  not null comment '创建人',
    update_by          varchar(64)  null comment '更新人',
    enable             tinyint      not null default 1 comment '是否启用 0 不启用,1 启用',
    is_del             tinyint      not null default 0 comment '是否逻辑删除 0.未删除 1.已删除'
);

drop table sys_user_job_record;
#入离职记录
create table sys_user_job_record
(
    id          varchar(64) primary key comment '主键',
    id_card     varchar(64) not null comment '身份证号码',
    create_time datetime    not null comment '创建时间',
    create_by   varchar(64) not null comment '创建人',
    action      tinyint     not null comment '入离职动作 1.入职  2.离职',
    contract    varchar(64) not null comment '合同号',
    remark      text        null comment '备注'
);

drop table sys_user_salary_contract;
#人员合同
create table sys_user_salary_contract
(
    id               varchar(64) primary key comment '主键',
    id_cais_masterrd varchar(64)             not null comment '身份证号码',
    tinyint                        default 1 not null comment '是否是主合同',
    post_station     varchar(20)             not null comment '岗位',
    post             varchar(20)             null comment '职务',
    salary           decimal(8, 2)           not null comment '标准薪酬',
    rest_days        int           default 0 not null comment '休息天数',
    working_hours    decimal(4, 1) default 8 not null comment '每日工作时长',
    create_time      datetime                not null comment '创建时间',
    update_time      datetime                null comment '更新时间',
    create_by        varchar(64)             not null comment '创建人',
    update_by        varchar(64)             null comment '更新人',
    enable           tinyint                 not null default 1 comment '是否启用 0 不启用,1 启用',
    is_del           tinyint                 not null default 0 comment '是否逻辑删除 0.未删除 1.已删除'
);

drop table if exists sys_project_contract;
-- auto-generated definition
create table if not exists sys_project_contract
(
    id             varchar(64)       not null comment '主键'
        primary key,
    depart_id      varchar(64)       not null comment '所属项目',
    depart_name    varchar(20)       null comment '部门名称',
    emp_num        int               not null comment '合同约定人数',
    pay_company    varchar(200)      null comment '付款公司',
    line           varchar(64)       null comment '条线',
    line_name      varchar(20)       null comment '条线名称',
    name           varchar(200)      not null comment '合同名称',
    type           tinyint default 0 not null comment '合同类型',
    type_name      varchar(20)       null comment '合同类型名称',
    effective_time datetime          not null comment '生效时间',
    dead_time      datetime          null comment '合同终止时间',
    month_salary   decimal(8, 2)     not null comment '总服务费',
    remark         text              null comment '备注',
    create_time    datetime          not null comment '创建时间',
    update_time    datetime          null comment '更新时间',
    create_by      varchar(64)       not null comment '创建人',
    update_by      varchar(64)       null comment '更新人',
    enable         tinyint default 1 not null comment '是否启用 0 不启用,1 启用',
    is_del         tinyint default 0 not null comment '是否逻辑删除 0.未删除 1.已删除'
)
    comment '项目服务合同';


drop table if exists sys_project_salary_config;
create table if not exists sys_project_salary_config
(
    id                varchar(64) primary key,
    remark            text              null comment '备注',
    depart_id         varchar(64)       not null comment '所属项目',
    depart_name       varchar(20)       null comment '部门名称',
    lowest_salary     decimal(8, 2)     not null comment '最低薪资',
    liability_premium decimal(8, 2)     not null comment '责任险',
    other             decimal(8, 2)     null comment '其他费用',
    create_time       datetime          not null comment '创建时间',
    update_time       datetime          null comment '更新时间',
    create_by         varchar(64)       not null comment '创建人',
    update_by         varchar(64)       null comment '更新人',
    enable            tinyint default 1 not null comment '是否启用 0 不启用,1 启用',
    is_del            tinyint default 0 not null comment '是否逻辑删除 0.未删除 1.已删除'
) comment '薪酬配置';


drop table if exists sys_project_weal_config;
create table if not exists sys_project_weal_config
(
    id          varchar(64) primary key,
    depart_id   varchar(64)       not null comment '所属项目',
    depart_name varchar(20)       null comment '部门名称',
    type        tinyint           null comment '补贴类型',
    money       decimal(8, 2)     not null comment '补贴金额',
    months      varchar(200)      null comment '生效月份',
    relyCheckIn tinyint           not null default 0 comment '是否依赖考勤',
    create_time datetime          not null comment '创建时间',
    update_time datetime          null comment '更新时间',
    create_by   varchar(64)       not null comment '创建人',
    update_by   varchar(64)       null comment '更新人',
    enable      tinyint default 1 not null comment '是否启用 0 不启用,1 启用',
    remark      text              null comment '备注',
    is_del      tinyint default 0 not null comment '是否逻辑删除 0.未删除 1.已删除'
) comment '福利配置';

drop table if exists check_in_user_month_salary;
create table if not exists check_in_user_month_salary
(
    id                     varchar(64) primary key,
    depart_id              varchar(64)       not null comment '所属项目',
    depart_name            varchar(20)       null comment '部门名称',
    month                  varchar(20)       null comment '月份',
    base_salary            decimal(8, 2)     not null comment '基础薪酬',
    other_salary           decimal(8, 2)     not null comment '其他薪酬',
    overtime_salary        decimal(8, 2)     not null comment '加班薪酬',
    high_temp_subsidy      decimal(8, 2)     not null comment '高温补贴',
    other_subsidy          decimal(8, 2)     not null comment '其他补贴',
    plus_salary            decimal(8, 2)     not null comment '调增',
    minus_salary           decimal(8, 2)     not null comment '调减',
    should_salary          decimal(8, 2)     not null comment '应发薪酬',
    medical_salary         decimal(8, 2)     not null comment '社保',
    provident_fund_salary  decimal(8, 2)     not null comment '公积金',
    liability_salary       decimal(8, 2)     not null comment '责任险',
    tax_deduction_salary   decimal(8, 2)     not null comment '个税扣减',
    total_deduction_salary decimal(8, 2)     not null comment '扣款合计',
    act_salary             decimal(8, 2)     not null comment '应发薪酬',
    create_time            datetime          not null comment '创建时间',
    update_time            datetime          null comment '更新时间',
    create_by              varchar(64)       not null comment '创建人',
    update_by              varchar(64)       null comment '更新人',
    enable                 tinyint default 1 not null comment '是否启用 0 不启用,1 启用',
    remark                 text              null comment '备注',
    is_del                 tinyint default 0 not null comment '是否逻辑删除 0.未删除 1.已删除'
) comment '人员月薪酬单';

drop table if exists sys_salary_modify_record;
create table if not exists sys_salary_modify_record
(
    id            varchar(64) primary key,
    depart_id     varchar(64)       not null comment '所属项目',
    depart_name   varchar(20)       null comment '部门名称',
    user_id       varchar(64)       not null comment '用户id',
    user_name     varchar(64)       not null comment '用户姓名',
    month         varchar(20)       not null comment '调整月份',
    type          tinyint default 1 not null comment '类型 1.调增 2.调减',
    adjust_salary decimal(8, 2)     not null comment '调整金额',
    create_time   datetime          not null comment '创建时间',
    update_time   datetime          null comment '更新时间',
    create_by     varchar(64)       not null comment '创建人',
    update_by     varchar(64)       null comment '更新人',
    enable        tinyint default 1 not null comment '是否启用 0 不启用,1 启用',
    remark        text              null comment '备注',
    is_del        tinyint default 0 not null comment '是否逻辑删除 0.未删除 1.已删除'
) comment '薪资调整记录';


drop table if exists daily_holiday_config;
create table if not exists daily_holiday_config
(
    id      varchar(64) primary key,
    year    varchar(10) comment '年份',
    month   varchar(20) comment '月份',
    after   boolean comment '是否补班',
    date    datetime comment '节假日日期',
    holiday boolean comment '是否法定假日',
    name    varchar(64) comment '节假日名称',
    rest    int comment '',
    target  varchar(64) comment '节假日',
    wage    tinyint comment '工资倍数'
)
    comment '国假法定节假日';