package org.jeecg.modules.alumni.inter;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.DateUtils;
import org.jeecg.modules.alumni.entity.AlumniActivity;
import org.jeecg.modules.alumni.enums.ActivityConstants;
import org.jeecg.modules.online.cgform.enhance.CgformEnhanceJavaInter;
import org.jeecg.modules.online.config.exception.BusinessException;
import org.springframework.stereotype.Component;
import java.util.Date;
import java.util.Objects;

/**
 * .
 *
 * <AUTHOR> goku
 * @version : 1.0 2023/10/26
 * @company 深圳市超智慧信息科技有限公司
 * @organization hypersmart
 * @team 611
 * @since : 1.0
 **/
@Slf4j
@Component("alumniActivityBefore")
public class AlumniActivityBeforeInter implements CgformEnhanceJavaInter {
    @Override
    public void execute(String s, JSONObject jsonObject) throws BusinessException {
    }
}
