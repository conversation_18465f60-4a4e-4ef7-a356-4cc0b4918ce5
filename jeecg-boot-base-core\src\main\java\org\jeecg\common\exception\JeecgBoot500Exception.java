package org.jeecg.common.exception;

import lombok.Data;

@Data
public class JeecgBoot500Exception extends RuntimeException{
    private Integer code;
    private String message;
    public JeecgBoot500Exception(Integer code, String message) {
        super(message);
        this.code=code;
        this.message=message;
    }

    public JeecgBoot500Exception(JeecgBootError error) {
        super(error.getMessage());
        this.code=error.getErrorCode();
        this.message=error.getMessage();
    }

}
