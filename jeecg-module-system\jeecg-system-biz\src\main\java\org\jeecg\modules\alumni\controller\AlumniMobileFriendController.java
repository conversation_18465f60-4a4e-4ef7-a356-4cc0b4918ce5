package org.jeecg.modules.alumni.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBoot500Exception;
import org.jeecg.modules.alumni.entity.AlumniUser;
import org.jeecg.modules.alumni.entity.AlumniUserFriendApply;
import org.jeecg.modules.alumni.entity.AlumniUserFriendRef;
import org.jeecg.modules.alumni.enums.FriendConstants;
import org.jeecg.modules.alumni.errors.FriendErrors;
import org.jeecg.modules.alumni.service.IAlumniUserFriendApplyService;
import org.jeecg.modules.alumni.service.IAlumniUserFriendRefService;
import org.jeecg.modules.alumni.service.IAlumniUserService;
import org.jeecg.modules.alumni.vo.AlumniFriendApplyVo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Api(tags = "移动端好友关系")
@RestController
@RequestMapping("/mobile/friend")
@Slf4j
public class AlumniMobileFriendController {
    @Resource
    IAlumniUserFriendRefService friendRefService;
    @Resource
    IAlumniUserFriendApplyService friendApplyService;
    @Resource
    IAlumniUserService userService;


    //我的所有好友请求列表
    @ApiOperation(value = "我的所有好友请求列表", notes = "我的所有好友请求列表")
    @PostMapping(value = "/applyList")
    public Result<?> applyList(@RequestParam(value = "userId") String userId,
                               @RequestParam(value = "userName",required = false) String userName,
                               @RequestParam(value = "pageNo") Integer pageNo,
                               @RequestParam(value = "pageSize") Integer pageSize) {
        IPage<AlumniFriendApplyVo> alumniFriendApplyVoIPage = friendApplyService.myFriendApplyPage(new Page<>(pageNo, pageSize), userId,userName);
        return Result.ok(alumniFriendApplyVoIPage);
    }


    //新的好友请求数量
    @ApiOperation(value = "新的好友请求数量", notes = "新的好友请求数量")
    @PostMapping(value = "/newApplyCount")
    public Result<?> newApplyCount(@RequestParam(value = "userId") String userId) {
        LambdaQueryWrapper<AlumniUserFriendApply> queryWrapper = new LambdaQueryWrapper<AlumniUserFriendApply>()
                .eq(AlumniUserFriendApply::getFriendId, userId)
                .eq(AlumniUserFriendApply::getStatus, FriendConstants.FRIEND_APPLY_STATUS_WAIT);
        long count = friendApplyService.count(queryWrapper);
        return Result.ok(count);
    }

    @ApiOperation(value = "好友请求", notes = "好友请求")
    @PostMapping(value = "/apply")
    public Result<?> apply(@RequestParam(value = "userId") String userId, @RequestParam(value = "friendId") String friendId) {
        AlumniUser me=userService.getById(userId);
        if(Objects.isNull(me)){
            throw new JeecgBoot500Exception(FriendErrors.userNotExist());
        }
        AlumniUser byId = userService.getById(friendId);
        if(Objects.isNull(byId)){
            throw new JeecgBoot500Exception(FriendErrors.friendNotExist());
        }
        //判断是否已经是好友
        if (friendRefService.isFriend(userId, friendId)) {
            throw new JeecgBoot500Exception(FriendErrors.friendAlreadyExist());
        }
        LambdaQueryWrapper<AlumniUserFriendApply> queryApplyWrapper = new LambdaQueryWrapper<>();
        queryApplyWrapper.eq(AlumniUserFriendApply::getUserId, userId)
                .eq(AlumniUserFriendApply::getFriendId, friendId);
        AlumniUserFriendApply friendApply = friendApplyService.getOne(queryApplyWrapper);
        if (Objects.nonNull(friendApply)) {
            if (FriendConstants.FRIEND_APPLY_STATUS_WAIT.equals(friendApply.getStatus())) {
                throw new JeecgBoot500Exception(FriendErrors.friendApplyAlreadyExist());
            }
            friendApply.setUpdateTime(new Date());
            friendApply.setStatus(FriendConstants.FRIEND_APPLY_STATUS_WAIT);
            friendApplyService.updateById(friendApply);
        } else {
            AlumniUserFriendApply alumniUserFriendApply = new AlumniUserFriendApply();
            alumniUserFriendApply.setFriendId(friendId);
            alumniUserFriendApply.setUserId(userId);
            alumniUserFriendApply.setStatus(FriendConstants.FRIEND_APPLY_STATUS_WAIT);
            alumniUserFriendApply.setApplyTime(new Date());
            alumniUserFriendApply.setCreateTime(new Date());
            alumniUserFriendApply.setUpdateTime(new Date());
            friendApplyService.save(alumniUserFriendApply);
        }
        return Result.ok();
    }

    @ApiOperation(value = "好友请求同意", notes = "好友请求同意")
    @PostMapping(value = "/applyAgree")
    public Result<?> applyAgree(@RequestParam(value = "friendApplyId") String friendApplyId, @RequestParam(value = "type", defaultValue = "1") Integer type) {
        LambdaQueryWrapper<AlumniUserFriendApply> queryApplyWrapper = new LambdaQueryWrapper<>();
        queryApplyWrapper.eq(AlumniUserFriendApply::getId, friendApplyId);
        AlumniUserFriendApply friendApply = friendApplyService.getOne(queryApplyWrapper);
        if (Objects.isNull(friendApply)) {
            throw new JeecgBoot500Exception(FriendErrors.friendApplyNotExist());
        }
        if(!FriendConstants.FRIEND_APPLY_STATUS_WAIT.equals(friendApply.getStatus())){
            throw new JeecgBoot500Exception(FriendErrors.friendApplyAlreadyExist());
        }
        friendApply.setStatus(FriendConstants.FRIEND_APPLY_STATUS_AGREE);
        friendApply.setApproveTime(new Date());
        friendApply.setUpdateTime(new Date());
        friendApplyService.updateById(friendApply);
        //判断是否已经是好友
        if (friendRefService.isFriend(friendApply.getUserId(), friendApply.getFriendId())) {
            throw new JeecgBoot500Exception(FriendErrors.friendAlreadyExist());
        }
        //创建好友关系
        AlumniUserFriendRef friendRef = new AlumniUserFriendRef();
        friendRef.setUserId(friendApply.getUserId());
        friendRef.setFriendId(friendApply.getFriendId());
        friendRefService.save(friendRef);
        //如果是交换名片,创建我是对方的好友
        if (type.equals(FriendConstants.FRIEND_REQ_EXCHANGE)) {
            if (!friendRefService.isFriend(friendApply.getFriendId(), friendApply.getUserId())) {
                AlumniUserFriendRef friendRef2 = new AlumniUserFriendRef();
                friendRef2.setUserId(friendApply.getFriendId());
                friendRef2.setFriendId(friendApply.getUserId());
                friendRefService.save(friendRef2);
            }
        }
        return Result.ok();
    }

    @ApiOperation(value = "好友请求拒绝", notes = "好友请求拒绝")
    @PostMapping(value = "/applyReject")
    public Result<?> applyReject(@RequestParam(value = "friendApplyId") String friendApplyId) {
        LambdaQueryWrapper<AlumniUserFriendApply> queryApplyWrapper = new LambdaQueryWrapper<>();
        queryApplyWrapper.eq(AlumniUserFriendApply::getId, friendApplyId)
                .eq(AlumniUserFriendApply::getStatus, FriendConstants.FRIEND_APPLY_STATUS_WAIT);
        AlumniUserFriendApply friendApply = friendApplyService.getOne(queryApplyWrapper);
        if (Objects.isNull(friendApply)) {
            throw new JeecgBoot500Exception(FriendErrors.friendApplyNotExist());
        }
        friendApply.setStatus(FriendConstants.FRIEND_APPLY_STATUS_REJECT);
        friendApplyService.updateById(friendApply);
        return Result.ok();
    }

    //我的好友列表
    @ApiOperation(value = "我的好友列表", notes = "我的好友列表")
    @PostMapping(value = "/myFriendList")
    public Result<?> myFriendList(@RequestParam(value = "userId") String userId,
                                  @RequestParam(value = "userName",required = false) String userName,
                                  @RequestParam(value = "pageNo",required = true) Integer pageNo,
                                  @RequestParam(value = "pageSize",required = true) Integer pageSize) {
        Page<AlumniUser> alumniUserPage = friendRefService.queryMyFriendPage(new Page<AlumniUser>(pageNo, pageSize), userId,userName);
        return Result.ok(alumniUserPage);
    }

}
